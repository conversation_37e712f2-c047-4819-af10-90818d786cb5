import React, { useState } from 'react';
import { ThemeProvider } from '../../contexts/ThemeContext';
import ModernLayout from '../../components/layout/ModernLayout';
import AdaptableCard from '../../components/ui/AdaptableCard';
import {
  FaHome,
  FaUser,
  FaBell,
  FaTruck,
  FaBox,
  FaRoute,
  FaChartBar,
  FaPlus,
  FaEye,
  FaMapMarkerAlt,
  FaClock,
  FaCheckCircle,
  FaExclamationTriangle,
  FaCalendarAlt,
} from 'react-icons/fa';

const ModernDashboardCourierPage = () => {
  const [showNewDelivery, setShowNewDelivery] = useState(false);

  // Configuration de la sidebar pour le courrier
  const sidebarItems = [
    {
      key: 'dashboard',
      path: '/courier/dashboard',
      title: 'Tableau de bord',
      icon: <FaHome />,
    },
    {
      key: 'profile',
      path: '/courier/profile',
      title: 'Mon Profil',
      icon: <FaUser />,
    },
    {
      key: 'deliveries',
      path: '/courier/deliveries',
      title: 'Mes Livraisons',
      icon: <FaTruck />,
      badge: '5',
    },
    {
      key: 'packages',
      path: '/courier/packages',
      title: 'Colis',
      icon: <FaBox />,
    },
    {
      key: 'routes',
      path: '/courier/routes',
      title: 'Itinéraires',
      icon: <FaRoute />,
    },
    {
      key: 'schedule',
      path: '/courier/schedule',
      title: 'Planning',
      icon: <FaCalendarAlt />,
    },
    {
      key: 'notifications',
      path: '/courier/notifications',
      title: 'Notifications',
      icon: <FaBell />,
      badge: '3',
    },
  ];

  // Données de démonstration pour le courrier
  const stats = [
    {
      title: 'Livraisons Aujourd\'hui',
      value: '12',
      change: '+3',
      changeType: 'positive',
      icon: <FaTruck className="text-blue-500" />,
    },
    {
      title: 'Colis en Transit',
      value: '8',
      change: '+2',
      changeType: 'positive',
      icon: <FaBox className="text-green-500" />,
    },
    {
      title: 'Livraisons Terminées',
      value: '45',
      change: '+12',
      changeType: 'positive',
      icon: <FaCheckCircle className="text-purple-500" />,
    },
    {
      title: 'Distance Parcourue',
      value: '156 km',
      change: '+25 km',
      changeType: 'positive',
      icon: <FaRoute className="text-orange-500" />,
    },
  ];

  const todayDeliveries = [
    {
      id: 1,
      recipient: 'SARL TECH SOLUTIONS',
      address: '123 Rue Mohammed V, Casablanca',
      type: 'Documents juridiques',
      status: 'En cours',
      time: '09:30',
      priority: 'high',
      statusColor: 'yellow',
    },
    {
      id: 2,
      recipient: 'SAS DIGITAL CORP',
      address: '456 Avenue Hassan II, Rabat',
      type: 'Attestation domiciliation',
      status: 'Livré',
      time: '11:15',
      priority: 'normal',
      statusColor: 'green',
    },
    {
      id: 3,
      recipient: 'SARL COMMERCE PLUS',
      address: '789 Boulevard Zerktouni, Casablanca',
      type: 'Contrat signé',
      status: 'Programmé',
      time: '14:00',
      priority: 'normal',
      statusColor: 'blue',
    },
    {
      id: 4,
      recipient: 'SNC SERVICES PRO',
      address: '321 Rue Allal Ben Abdellah, Salé',
      type: 'Documents comptables',
      status: 'Urgent',
      time: '16:30',
      priority: 'high',
      statusColor: 'red',
    },
  ];

  const quickActions = [
    {
      title: 'Nouvelle Livraison',
      description: 'Ajouter une livraison au planning',
      icon: <FaPlus className="text-blue-500" />,
      action: () => setShowNewDelivery(true),
    },
    {
      title: 'Voir Itinéraire',
      description: 'Optimiser le parcours du jour',
      icon: <FaRoute className="text-green-500" />,
      action: () => console.log('Itinéraire'),
    },
    {
      title: 'Scanner Colis',
      description: 'Scanner un code-barres',
      icon: <FaBox className="text-purple-500" />,
      action: () => console.log('Scanner'),
    },
    {
      title: 'Rapport Journalier',
      description: 'Générer le rapport du jour',
      icon: <FaChartBar className="text-orange-500" />,
      action: () => console.log('Rapport'),
    },
  ];

  const getPriorityIcon = (priority: string) => {
    if (priority === 'high') {
      return <FaExclamationTriangle className="text-red-500" />;
    }
    return <FaClock className="text-gray-400" />;
  };

  return (
    <ThemeProvider>
      <ModernLayout sidebarItems={sidebarItems} userType="courier">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Dashboard Courrier
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Gérez vos livraisons et optimisez vos tournées
              </p>
            </div>
            <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors duration-200">
              <FaPlus />
              <span>Nouvelle livraison</span>
            </button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <AdaptableCard key={index} className="hover:shadow-lg transition-shadow duration-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                      {stat.value}
                    </p>
                    <p className={`text-sm mt-1 ${
                      stat.changeType === 'positive' 
                        ? 'text-green-600 dark:text-green-400' 
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {stat.change}
                    </p>
                  </div>
                  <div className="text-2xl">
                    {stat.icon}
                  </div>
                </div>
              </AdaptableCard>
            ))}
          </div>

          {/* Quick Actions */}
          <AdaptableCard>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Actions rapides
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {quickActions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.action}
                  className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200 text-left"
                >
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">
                      {action.icon}
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {action.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {action.description}
                      </p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </AdaptableCard>

          {/* Today's Deliveries */}
          <AdaptableCard>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Livraisons du jour
              </h2>
              <button className="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                Voir planning complet
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Destinataire</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Adresse</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Type</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Heure</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Statut</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {todayDeliveries.map((delivery) => (
                    <tr key={delivery.id} className="border-b border-gray-100 dark:border-gray-800">
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-2">
                          {getPriorityIcon(delivery.priority)}
                          <span className="text-gray-900 dark:text-white">{delivery.recipient}</span>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-1">
                          <FaMapMarkerAlt className="text-gray-400 text-sm" />
                          <span className="text-gray-600 dark:text-gray-400 text-sm">{delivery.address}</span>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-gray-900 dark:text-white">{delivery.type}</td>
                      <td className="py-3 px-4 text-gray-600 dark:text-gray-400">{delivery.time}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          delivery.statusColor === 'green' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : delivery.statusColor === 'yellow'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                            : delivery.statusColor === 'red'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                        }`}>
                          {delivery.status}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <button className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200">
                            <FaEye />
                          </button>
                          <button className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200">
                            <FaMapMarkerAlt />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </AdaptableCard>
        </div>
      </ModernLayout>
    </ThemeProvider>
  );
};

export default ModernDashboardCourierPage;

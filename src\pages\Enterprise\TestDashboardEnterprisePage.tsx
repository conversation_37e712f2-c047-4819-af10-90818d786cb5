import React from 'react';

const TestDashboardEnterprisePage = () => {
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Test Dashboard Entreprise
        </h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Test de base
          </h2>
          <p className="text-gray-600">
            Si vous voyez ce texte, le composant de base fonctionne.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800">Clients Actifs</h3>
            <p className="text-3xl font-bold text-blue-600 mt-2">156</p>
            <p className="text-sm text-green-600 mt-1">+12.5%</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800">Domiciliations</h3>
            <p className="text-3xl font-bold text-green-600 mt-2">89</p>
            <p className="text-sm text-green-600 mt-1">+8</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800">Services Juridiques</h3>
            <p className="text-3xl font-bold text-purple-600 mt-2">34</p>
            <p className="text-sm text-green-600 mt-1">+5</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800">Chiffre d'Affaires</h3>
            <p className="text-3xl font-bold text-orange-600 mt-2">245K MAD</p>
            <p className="text-sm text-green-600 mt-1">+15.2%</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Actions rapides
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <h3 className="font-medium text-gray-900">Nouvelle Domiciliation</h3>
              <p className="text-sm text-gray-600">Créer une domiciliation pour un client</p>
            </button>
            
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <h3 className="font-medium text-gray-900">Ajouter une Offre</h3>
              <p className="text-sm text-gray-600">Créer une nouvelle offre de service</p>
            </button>
            
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <h3 className="font-medium text-gray-900">Gestion Clients</h3>
              <p className="text-sm text-gray-600">Voir et gérer les clients</p>
            </button>
            
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
              <h3 className="font-medium text-gray-900">Rapports</h3>
              <p className="text-sm text-gray-600">Générer des rapports d'activité</p>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestDashboardEnterprisePage;

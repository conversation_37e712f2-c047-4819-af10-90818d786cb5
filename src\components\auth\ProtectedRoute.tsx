import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthContext } from '../../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'client' | 'enterprise' | 'courier' | 'admin';
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredRole,
  redirectTo = '/signin'
}) => {
  const { user, loading } = useAuthContext();
  const location = useLocation();

  // Afficher un loader pendant la vérification de l'authentification
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Vérification de l'authentification...</p>
        </div>
      </div>
    );
  }

  // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
  if (!user) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // Vérifier le rôle si requis
  if (requiredRole && user.role !== requiredRole) {
    // Rediriger vers le dashboard approprié selon le rôle de l'utilisateur
    const userDashboard = getUserDashboardPath(user.role);
    return <Navigate to={userDashboard} replace />;
  }

  return <>{children}</>;
};

// Fonction utilitaire pour obtenir le chemin du dashboard selon le rôle
const getUserDashboardPath = (role: string): string => {
  switch (role) {
    case 'admin':
      return '/admin/dashboard';
    case 'enterprise':
    case 'entreprise':
      return '/enterprise/modern-dashboard';
    case 'courier':
    case 'coursier':
      return '/courier/modern-dashboard';
    case 'client':
    default:
      return '/client/modern-dashboard';
  }
};

export default ProtectedRoute;

import React, { useState } from 'react';
import { ThemeProvider } from '../../contexts/ThemeContext';
import ModernLayout from '../../components/layout/ModernLayout';
import AdaptableCard from '../../components/ui/AdaptableCard';
import {
  FaHome,
  FaUser,
  FaBell,
  FaBuilding,
  FaFileAlt,
  FaCalculator,
  FaChartBar,
  FaPlus,
  FaEye,
  FaDownload,
  FaUsers,
  FaCog,
  FaClipboardList,
} from 'react-icons/fa';

const ModernDashboardEnterprisePage = () => {
  console.log('ModernDashboardEnterprisePage is rendering');

  // Test ultra-simple d'abord
  return (
    <div style={{ padding: '20px', backgroundColor: '#f0f0f0', minHeight: '100vh' }}>
      <h1 style={{ color: 'red', fontSize: '24px' }}>DASHBOARD ENTREPRISE - TEST</h1>
      <p>Si vous voyez ce texte, le composant se charge.</p>
      <button onClick={() => alert('Test OK')} style={{ padding: '10px', backgroundColor: 'blue', color: 'white' }}>
        Cliquer ici
      </button>
    </div>
  );

  const [showDomiciliationForm, setShowDomiciliationForm] = useState(false);

  // Configuration de la sidebar pour l'entreprise
  const sidebarItems = [
    {
      key: 'dashboard',
      path: '/enterprise/dashboard',
      title: 'Tableau de bord',
      icon: <FaHome />,
    },
    {
      key: 'profile',
      path: '/enterprise/profile',
      title: 'Profil Entreprise',
      icon: <FaBuilding />,
    },
    {
      key: 'domiciliation',
      path: '/enterprise/domiciliation',
      title: 'Domiciliation',
      icon: <FaBuilding />,
    },
    {
      key: 'juridique',
      path: '/enterprise/juridique',
      title: 'Services Juridiques',
      icon: <FaFileAlt />,
    },
    {
      key: 'offres',
      path: '/enterprise/offres',
      title: 'Gestion des Offres',
      icon: <FaClipboardList />,
    },
    {
      key: 'annonces',
      path: '/enterprise/annonces',
      title: 'Annonces',
      icon: <FaBell />,
    },
    {
      key: 'comptabilite',
      path: '/enterprise/comptabilite',
      title: 'Comptabilité',
      icon: <FaCalculator />,
    },
    {
      key: 'clients',
      path: '/enterprise/clients',
      title: 'Gestion Clients',
      icon: <FaUsers />,
    },
    {
      key: 'settings',
      path: '/enterprise/settings',
      title: 'Paramètres',
      icon: <FaCog />,
    },
  ];

  // Données de démonstration pour l'entreprise
  const stats = [
    {
      title: 'Clients Actifs',
      value: '156',
      change: '+12.5%',
      changeType: 'positive',
      icon: <FaUsers className="text-blue-500" />,
    },
    {
      title: 'Domiciliations',
      value: '89',
      change: '+8',
      changeType: 'positive',
      icon: <FaBuilding className="text-green-500" />,
    },
    {
      title: 'Services Juridiques',
      value: '34',
      change: '+5',
      changeType: 'positive',
      icon: <FaFileAlt className="text-purple-500" />,
    },
    {
      title: 'Chiffre d\'Affaires',
      value: '245K MAD',
      change: '+15.2%',
      changeType: 'positive',
      icon: <FaChartBar className="text-orange-500" />,
    },
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'Nouvelle Domiciliation',
      client: 'SARL TECH INNOVATIONS',
      status: 'En cours',
      date: '2024-01-15',
      amount: '2,500 MAD',
      statusColor: 'yellow',
    },
    {
      id: 2,
      type: 'Service Juridique',
      client: 'SAS DIGITAL SOLUTIONS',
      status: 'Terminé',
      date: '2024-01-14',
      amount: '5,000 MAD',
      statusColor: 'green',
    },
    {
      id: 3,
      type: 'Comptabilité',
      client: 'SARL COMMERCE PLUS',
      status: 'En attente',
      date: '2024-01-13',
      amount: '1,800 MAD',
      statusColor: 'blue',
    },
    {
      id: 4,
      type: 'Domiciliation',
      client: 'SNC SERVICES PRO',
      status: 'Actif',
      date: '2024-01-12',
      amount: '3,200 MAD',
      statusColor: 'green',
    },
  ];

  const quickActions = [
    {
      title: 'Nouvelle Domiciliation',
      description: 'Créer une domiciliation pour un client',
      icon: <FaBuilding className="text-blue-500" />,
      action: () => setShowDomiciliationForm(true),
    },
    {
      title: 'Ajouter une Offre',
      description: 'Créer une nouvelle offre de service',
      icon: <FaPlus className="text-green-500" />,
      action: () => console.log('Nouvelle offre'),
    },
    {
      title: 'Gestion Clients',
      description: 'Voir et gérer les clients',
      icon: <FaUsers className="text-purple-500" />,
      action: () => console.log('Gestion clients'),
    },
    {
      title: 'Rapports',
      description: 'Générer des rapports d\'activité',
      icon: <FaChartBar className="text-orange-500" />,
      action: () => console.log('Rapports'),
    },
  ];

  // Version de debug - affichage simple d'abord
  const isDebugMode = true;

  if (isDebugMode) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f3f4f6',
        padding: '2rem'
      }}>
        <div style={{ maxWidth: '1280px', margin: '0 auto' }}>
          <h1 style={{
            fontSize: '2rem',
            fontWeight: 'bold',
            color: '#111827',
            marginBottom: '2rem'
          }}>
            Dashboard Entreprise (Debug Mode)
          </h1>

          <div style={{
            backgroundColor: 'white',
            borderRadius: '0.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            padding: '1.5rem',
            marginBottom: '1.5rem'
          }}>
            <h2 style={{
              fontSize: '1.25rem',
              fontWeight: '600',
              color: '#1f2937',
              marginBottom: '1rem'
            }}>
              Test de fonctionnement
            </h2>
            <p style={{ color: '#6b7280', marginBottom: '1rem' }}>
              Si vous voyez ce texte, le composant de base fonctionne.
            </p>
            <button
              onClick={() => {
                console.log('Button clicked');
                alert('Button fonctionne !');
              }}
              style={{
                backgroundColor: '#3b82f6',
                color: 'white',
                padding: '0.5rem 1rem',
                borderRadius: '0.25rem',
                border: 'none',
                cursor: 'pointer'
              }}
            >
              Test Button
            </button>
          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1.5rem'
          }}>
            {stats.map((stat, index) => (
              <div key={index} style={{
                backgroundColor: 'white',
                borderRadius: '0.5rem',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '1.5rem'
              }}>
                <h3 style={{
                  fontSize: '1.125rem',
                  fontWeight: '500',
                  color: '#1f2937'
                }}>
                  {stat.title}
                </h3>
                <p style={{
                  fontSize: '1.875rem',
                  fontWeight: 'bold',
                  color: '#3b82f6',
                  marginTop: '0.5rem'
                }}>
                  {stat.value}
                </p>
                <p style={{
                  fontSize: '0.875rem',
                  color: '#10b981',
                  marginTop: '0.25rem'
                }}>
                  {stat.change}
                </p>
              </div>
            ))}
          </div>

          <div style={{
            backgroundColor: 'white',
            borderRadius: '0.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            padding: '1.5rem',
            marginTop: '1.5rem'
          }}>
            <h2 style={{
              fontSize: '1.25rem',
              fontWeight: '600',
              color: '#1f2937',
              marginBottom: '1rem'
            }}>
              Actions rapides
            </h2>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '1rem'
            }}>
              {quickActions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.action}
                  style={{
                    padding: '1rem',
                    border: '1px solid #e5e7eb',
                    borderRadius: '0.5rem',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    textAlign: 'left'
                  }}
                >
                  <h3 style={{
                    fontWeight: '500',
                    color: '#111827',
                    marginBottom: '0.25rem'
                  }}>
                    {action.title}
                  </h3>
                  <p style={{
                    fontSize: '0.875rem',
                    color: '#6b7280'
                  }}>
                    {action.description}
                  </p>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <ThemeProvider>
      <ModernLayout sidebarItems={sidebarItems} userType="enterprise">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Dashboard Entreprise
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Gérez vos services et clients efficacement
              </p>
            </div>
            <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors duration-200">
              <FaPlus />
              <span>Nouveau service</span>
            </button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <AdaptableCard key={index} className="hover:shadow-lg transition-shadow duration-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                      {stat.value}
                    </p>
                    <p className={`text-sm mt-1 ${
                      stat.changeType === 'positive' 
                        ? 'text-green-600 dark:text-green-400' 
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {stat.change}
                    </p>
                  </div>
                  <div className="text-2xl">
                    {stat.icon}
                  </div>
                </div>
              </AdaptableCard>
            ))}
          </div>

          {/* Quick Actions */}
          <AdaptableCard>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Actions rapides
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {quickActions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.action}
                  className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200 text-left"
                >
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">
                      {action.icon}
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {action.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {action.description}
                      </p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </AdaptableCard>

          {/* Recent Activities */}
          <AdaptableCard>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Activités récentes
              </h2>
              <button className="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                Voir tout
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Type</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Client</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Statut</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Date</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Montant</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {recentActivities.map((activity) => (
                    <tr key={activity.id} className="border-b border-gray-100 dark:border-gray-800">
                      <td className="py-3 px-4 text-gray-900 dark:text-white">{activity.type}</td>
                      <td className="py-3 px-4 text-gray-900 dark:text-white">{activity.client}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          activity.statusColor === 'green' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : activity.statusColor === 'yellow'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                            : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                        }`}>
                          {activity.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-gray-600 dark:text-gray-400">{activity.date}</td>
                      <td className="py-3 px-4 text-gray-900 dark:text-white font-medium">{activity.amount}</td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <button className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200">
                            <FaEye />
                          </button>
                          <button className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200">
                            <FaDownload />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </AdaptableCard>
        </div>
      </ModernLayout>
    </ThemeProvider>
  );
};

export default ModernDashboardEnterprisePage;

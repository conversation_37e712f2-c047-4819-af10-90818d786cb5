(()=>{var $;function I(G){return I=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},I(G)}function x(G,H){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);H&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(G,Y).enumerable})),J.push.apply(J,X)}return J}function q(G){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?x(Object(J),!0).forEach(function(X){N(G,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):x(Object(J)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(J,X))})}return G}function N(G,H,J){if(H=W(H),H in G)Object.defineProperty(G,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[H]=J;return G}function W(G){var H=z(G,"string");return I(H)=="symbol"?H:String(H)}function z(G,H){if(I(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var X=J.call(G,H||"default");if(I(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var A=Object.defineProperty,JG=function G(H,J){for(var X in J)A(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Y(Z){return J[X]=function(){return Z}}})},D={lessThanXSeconds:{one:"1 \u0441\u043E\u043D\u0438\u044F\u0434\u0430\u043D \u043A\u0430\u043C",other:"{{count}} \u0441\u043E\u043D\u0438\u044F\u0434\u0430\u043D \u043A\u0430\u043C"},xSeconds:{one:"1 \u0441\u043E\u043D\u0438\u044F",other:"{{count}} \u0441\u043E\u043D\u0438\u044F"},halfAMinute:"\u044F\u0440\u0438\u043C \u0434\u0430\u049B\u0438\u049B\u0430",lessThanXMinutes:{one:"1 \u0434\u0430\u049B\u0438\u049B\u0430\u0434\u0430\u043D \u043A\u0430\u043C",other:"{{count}} \u0434\u0430\u049B\u0438\u049B\u0430\u0434\u0430\u043D \u043A\u0430\u043C"},xMinutes:{one:"1 \u0434\u0430\u049B\u0438\u049B\u0430",other:"{{count}} \u0434\u0430\u049B\u0438\u049B\u0430"},aboutXHours:{one:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D 1 \u0441\u043E\u0430\u0442",other:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D {{count}} \u0441\u043E\u0430\u0442"},xHours:{one:"1 \u0441\u043E\u0430\u0442",other:"{{count}} \u0441\u043E\u0430\u0442"},xDays:{one:"1 \u043A\u0443\u043D",other:"{{count}} \u043A\u0443\u043D"},aboutXWeeks:{one:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D 1 \u0445\u0430\u0444\u0442\u0430",other:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D {{count}} \u0445\u0430\u0444\u0442\u0430"},xWeeks:{one:"1 \u0445\u0430\u0444\u0442\u0430",other:"{{count}} \u0445\u0430\u0444\u0442\u0430"},aboutXMonths:{one:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D 1 \u043E\u0439",other:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D {{count}} \u043E\u0439"},xMonths:{one:"1 \u043E\u0439",other:"{{count}} \u043E\u0439"},aboutXYears:{one:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D 1 \u0439\u0438\u043B",other:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D {{count}} \u0439\u0438\u043B"},xYears:{one:"1 \u0439\u0438\u043B",other:"{{count}} \u0439\u0438\u043B"},overXYears:{one:"1 \u0439\u0438\u043B\u0434\u0430\u043D \u043A\u045E\u043F",other:"{{count}} \u0439\u0438\u043B\u0434\u0430\u043D \u043A\u045E\u043F"},almostXYears:{one:"\u0434\u0435\u044F\u0440\u043B\u0438 1 \u0439\u0438\u043B",other:"\u0434\u0435\u044F\u0440\u043B\u0438 {{count}} \u0439\u0438\u043B"}},S=function G(H,J,X){var Y,Z=D[H];if(typeof Z==="string")Y=Z;else if(J===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(J));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return Y+"\u0434\u0430\u043D \u043A\u0435\u0439\u0438\u043D";else return Y+" \u043E\u043B\u0434\u0438\u043D";return Y};function E(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):G.defaultWidth,X=G.formats[J]||G.formats[G.defaultWidth];return X}}var M={full:"EEEE, do MMMM, y",long:"do MMMM, y",medium:"d MMM, y",short:"dd/MM/yyyy"},R={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},L={any:"{{date}}, {{time}}"},V={date:E({formats:M,defaultWidth:"full"}),time:E({formats:R,defaultWidth:"full"}),dateTime:E({formats:L,defaultWidth:"any"})},j={lastWeek:"'\u045E\u0442\u0433\u0430\u043D' eeee p '\u0434\u0430'",yesterday:"'\u043A\u0435\u0447\u0430' p '\u0434\u0430'",today:"'\u0431\u0443\u0433\u0443\u043D' p '\u0434\u0430'",tomorrow:"'\u044D\u0440\u0442\u0430\u0433\u0430' p '\u0434\u0430'",nextWeek:"eeee p '\u0434\u0430'",other:"P"},w=function G(H,J,X,Y){return j[H]};function O(G){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Y;if(X==="formatting"&&G.formattingValues){var Z=G.defaultFormattingWidth||G.defaultWidth,B=J!==null&&J!==void 0&&J.width?String(J.width):Z;Y=G.formattingValues[B]||G.formattingValues[Z]}else{var T=G.defaultWidth,C=J!==null&&J!==void 0&&J.width?String(J.width):G.defaultWidth;Y=G.values[C]||G.values[T]}var U=G.argumentCallback?G.argumentCallback(H):H;return Y[U]}}var _={narrow:["\u041C.\u0410","\u041C"],abbreviated:["\u041C.\u0410","\u041C"],wide:["\u041C\u0438\u043B\u043E\u0434\u0434\u0430\u043D \u0410\u0432\u0432\u0430\u043B\u0433\u0438","\u041C\u0438\u043B\u043E\u0434\u0438\u0439"]},f={narrow:["1","2","3","4"],abbreviated:["1-\u0447\u043E\u0440.","2-\u0447\u043E\u0440.","3-\u0447\u043E\u0440.","4-\u0447\u043E\u0440."],wide:["1-\u0447\u043E\u0440\u0430\u043A","2-\u0447\u043E\u0440\u0430\u043A","3-\u0447\u043E\u0440\u0430\u043A","4-\u0447\u043E\u0440\u0430\u043A"]},F={narrow:["\u042F","\u0424","\u041C","\u0410","\u041C","\u0418","\u0418","\u0410","\u0421","\u041E","\u041D","\u0414"],abbreviated:["\u044F\u043D\u0432","\u0444\u0435\u0432","\u043C\u0430\u0440","\u0430\u043F\u0440","\u043C\u0430\u0439","\u0438\u044E\u043D","\u0438\u044E\u043B","\u0430\u0432\u0433","\u0441\u0435\u043D","\u043E\u043A\u0442","\u043D\u043E\u044F","\u0434\u0435\u043A"],wide:["\u044F\u043D\u0432\u0430\u0440","\u0444\u0435\u0432\u0440\u0430\u043B","\u043C\u0430\u0440\u0442","\u0430\u043F\u0440\u0435\u043B","\u043C\u0430\u0439","\u0438\u044E\u043D","\u0438\u044E\u043B","\u0430\u0432\u0433\u0443\u0441\u0442","\u0441\u0435\u043D\u0442\u0430\u0431\u0440","\u043E\u043A\u0442\u0430\u0431\u0440","\u043D\u043E\u044F\u0431\u0440","\u0434\u0435\u043A\u0430\u0431\u0440"]},v={narrow:["\u042F","\u0414","\u0421","\u0427","\u041F","\u0416","\u0428"],short:["\u044F\u043A","\u0434\u0443","\u0441\u0435","\u0447\u043E","\u043F\u0430","\u0436\u0443","\u0448\u0430"],abbreviated:["\u044F\u043A\u0448","\u0434\u0443\u0448","\u0441\u0435\u0448","\u0447\u043E\u0440","\u043F\u0430\u0439","\u0436\u0443\u043C","\u0448\u0430\u043D"],wide:["\u044F\u043A\u0448\u0430\u043D\u0431\u0430","\u0434\u0443\u0448\u0430\u043D\u0431\u0430","\u0441\u0435\u0448\u0430\u043D\u0431\u0430","\u0447\u043E\u0440\u0448\u0430\u043D\u0431\u0430","\u043F\u0430\u0439\u0448\u0430\u043D\u0431\u0430","\u0436\u0443\u043C\u0430","\u0448\u0430\u043D\u0431\u0430"]},P={any:{am:"\u041F.\u041E.",pm:"\u041F.\u041A.",midnight:"\u044F\u0440\u0438\u043C \u0442\u0443\u043D",noon:"\u043F\u0435\u0448\u0438\u043D",morning:"\u044D\u0440\u0442\u0430\u043B\u0430\u0431",afternoon:"\u043F\u0435\u0448\u0438\u043D\u0434\u0430\u043D \u043A\u0435\u0439\u0438\u043D",evening:"\u043A\u0435\u0447\u0430\u0441\u0438",night:"\u0442\u0443\u043D"}},k={any:{am:"\u041F.\u041E.",pm:"\u041F.\u041A.",midnight:"\u044F\u0440\u0438\u043C \u0442\u0443\u043D",noon:"\u043F\u0435\u0448\u0438\u043D",morning:"\u044D\u0440\u0442\u0430\u043B\u0430\u0431",afternoon:"\u043F\u0435\u0448\u0438\u043D\u0434\u0430\u043D \u043A\u0435\u0439\u0438\u043D",evening:"\u043A\u0435\u0447\u0430\u0441\u0438",night:"\u0442\u0443\u043D"}},b=function G(H,J){return String(H)},h={ordinalNumber:b,era:O({values:_,defaultWidth:"wide"}),quarter:O({values:f,defaultWidth:"wide",argumentCallback:function G(H){return H-1}}),month:O({values:F,defaultWidth:"wide"}),day:O({values:v,defaultWidth:"wide"}),dayPeriod:O({values:P,defaultWidth:"any",formattingValues:k,defaultFormattingWidth:"any"})};function Q(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Y=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Z=H.match(Y);if(!Z)return null;var B=Z[0],T=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],C=Array.isArray(T)?c(T,function(K){return K.test(B)}):m(T,function(K){return K.test(B)}),U;U=G.valueCallback?G.valueCallback(C):C,U=J.valueCallback?J.valueCallback(U):U;var HG=H.slice(B.length);return{value:U,rest:HG}}}function m(G,H){for(var J in G)if(Object.prototype.hasOwnProperty.call(G,J)&&H(G[J]))return J;return}function c(G,H){for(var J=0;J<G.length;J++)if(H(G[J]))return J;return}function y(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(G.matchPattern);if(!X)return null;var Y=X[0],Z=H.match(G.parsePattern);if(!Z)return null;var B=G.valueCallback?G.valueCallback(Z[0]):Z[0];B=J.valueCallback?J.valueCallback(B):B;var T=H.slice(Y.length);return{value:B,rest:T}}}var p=/^(\d+)(чи)?/i,d=/\d+/i,g={narrow:/^(м\.а|м\.)/i,abbreviated:/^(м\.а|м\.)/i,wide:/^(милоддан аввал|милоддан кейин)/i},u={any:[/^м/i,/^а/i]},l={narrow:/^[1234]/i,abbreviated:/^[1234]-чор./i,wide:/^[1234]-чорак/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[яфмамииасонд]/i,abbreviated:/^(янв|фев|мар|апр|май|июн|июл|авг|сен|окт|ноя|дек)/i,wide:/^(январ|феврал|март|апрел|май|июн|июл|август|сентабр|октабр|ноябр|декабр)/i},s={narrow:[/^я/i,/^ф/i,/^м/i,/^а/i,/^м/i,/^и/i,/^и/i,/^а/i,/^с/i,/^о/i,/^н/i,/^д/i],any:[/^я/i,/^ф/i,/^мар/i,/^ап/i,/^май/i,/^июн/i,/^июл/i,/^ав/i,/^с/i,/^о/i,/^н/i,/^д/i]},o={narrow:/^[ядсчпжш]/i,short:/^(як|ду|се|чо|па|жу|ша)/i,abbreviated:/^(якш|душ|сеш|чор|пай|жум|шан)/i,wide:/^(якшанба|душанба|сешанба|чоршанба|пайшанба|жума|шанба)/i},r={narrow:[/^я/i,/^д/i,/^с/i,/^ч/i,/^п/i,/^ж/i,/^ш/i],any:[/^як/i,/^ду/i,/^се/i,/^чор/i,/^пай/i,/^жу/i,/^шан/i]},a={any:/^(п\.о\.|п\.к\.|ярим тун|пешиндан кейин|(эрталаб|пешиндан кейин|кечаси|тун))/i},e={any:{am:/^п\.о\./i,pm:/^п\.к\./i,midnight:/^ярим тун/i,noon:/^пешиндан кейин/i,morning:/эрталаб/i,afternoon:/пешиндан кейин/i,evening:/кечаси/i,night:/тун/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:d,valueCallback:function G(H){return parseInt(H,10)}}),era:Q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:Q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},GG={code:"uz-Cyrl",formatDistance:S,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=q(q({},window.dateFns),{},{locale:q(q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{uzCyrl:GG})})})();

//# debugId=B6299DEA31F3F74464756E2164756E21

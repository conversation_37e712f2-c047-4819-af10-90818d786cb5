import React, { useState } from 'react';
import { FaEdit, FaChevronDown, FaChevronUp } from 'react-icons/fa';

// Mock data for documents organized by sections
const mockDocumentSections = [
  {
    id: 1,
    title: 'Documents Juridiques',
    documents: [
      { id: 1, name: 'Certificat Négatif' },
      { id: 2, name: 'Dossier Juridiques' },
      { id: 3, name: 'Enregistrement Dossier' },
    ]
  },
  {
    id: 2,
    title: 'Documents Commerciaux',
    documents: [
      { id: 4, name: '<PERSON>e' },
      { id: 5, name: 'Registre de commerce' },
      { id: 6, name: 'RC Model 7' },
    ]
  },
  {
    id: 3,
    title: 'Documents Fiscaux',
    documents: [
      { id: 7, name: 'Identifiant Fiscal' },
      { id: 8, name: 'Code Sample' },
    ]
  },
  {
    id: 4,
    title: 'Documents CNSS',
    documents: [
      { id: 9, name: 'Demande CNSS' },
      { id: 10, name: 'Attestation CNSS' },
    ]
  },
];

interface DocumentsListPageProps {
  orderId?: number;
  orderReference?: string;
  onClose?: () => void;
}

const DocumentsListPage: React.FC<DocumentsListPageProps> = ({
  orderId,
  orderReference,
  onClose
}) => {
  // State to manage which sections are expanded
  const [expandedSections, setExpandedSections] = useState<Set<number>>(new Set());

  // Handle toggle section
  const handleToggleSection = (sectionId: number) => {
    const newExpandedSections = new Set(expandedSections);
    if (newExpandedSections.has(sectionId)) {
      newExpandedSections.delete(sectionId);
    } else {
      newExpandedSections.add(sectionId);
    }
    setExpandedSections(newExpandedSections);
  };

  // Handle edit document
  const handleEditDocument = (id: number) => {
    // Implement edit document functionality
    console.log('Edit document with ID:', id);
  };

  return (
    <div className="bg-white p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Documents Juridiques</h2>
          {orderReference && (
            <p className="text-gray-600">
              Dossier: {orderReference} {orderId && `(ID: ${orderId})`}
            </p>
          )}
        </div>

        {/* Document Sections */}
        <div className="space-y-6">
          {mockDocumentSections.map((section) => {
            const isExpanded = expandedSections.has(section.id);

            return (
              <div key={section.id} className="bg-white rounded-lg shadow-md border border-gray-200">
                {/* Section Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50 rounded-t-lg">
                  <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                  <button
                    onClick={() => handleToggleSection(section.id)}
                    className="text-blue-500 hover:text-blue-700 transition-colors p-2 rounded-full hover:bg-blue-50"
                    title={isExpanded ? "Réduire" : "Développer"}
                  >
                    {isExpanded ? (
                      <FaChevronUp className="w-4 h-4" />
                    ) : (
                      <FaChevronDown className="w-4 h-4" />
                    )}
                  </button>
                </div>

                {/* Section Content */}
                {isExpanded && (
                  <div className="p-4">
                    <div className="space-y-3">
                      {section.documents.map((document) => (
                        <div key={document.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                              <span className="text-blue-600 text-sm font-medium">📄</span>
                            </div>
                            <span className="text-sm font-medium text-gray-900">{document.name}</span>
                          </div>

                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleEditDocument(document.id)}
                              className="text-green-500 hover:text-green-700 transition-colors p-2 rounded-full hover:bg-green-50"
                              title="Éditer"
                            >
                              <FaEdit className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default DocumentsListPage;

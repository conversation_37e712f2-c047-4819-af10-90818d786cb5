import React from 'react';
import { FaEdit } from 'react-icons/fa';

// Mock data for documents
const mockDocuments = [
  { id: 1, name: 'Certificat Negative' },
  { id: 2, name: 'Dossier Juridiques' },
  { id: 3, name: 'Enregistrement Dossier' },
  { id: 4, name: '<PERSON><PERSON>' },
  { id: 5, name: 'Registe de commerce' },
  { id: 6, name: 'RC Model 7' },
  { id: 7, name: 'Identifiant Fiscale' },
  { id: 8, name: 'Code Sample' },
  { id: 9, name: '<PERSON><PERSON><PERSON> CNSS' },
  { id: 10, name: 'Attestation CNSS' },
];

interface DocumentsListPageProps {
  orderId?: number;
  orderReference?: string;
  onClose?: () => void;
}

const DocumentsListPage: React.FC<DocumentsListPageProps> = ({
  orderId,
  orderReference,
  onClose
}) => {
  // Handle edit document
  const handleEditDocument = (id: number) => {
    // Implement edit document functionality
    console.log('Edit document with ID:', id);
  };

  return (
    <div className="bg-white p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Documents Juridiques</h2>
          {orderReference && (
            <p className="text-gray-600">
              Dossier: {orderReference} {orderId && `(ID: ${orderId})`}
            </p>
          )}
        </div>

        {/* Documents List */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-xl font-semibold text-blue-600 mb-6">Liste des Documents</h3>

          <div className="space-y-4">
            {mockDocuments.map((document) => (
              <div key={document.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 text-sm font-medium">📄</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">{document.name}</span>
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => handleEditDocument(document.id)}
                    className="text-green-500 hover:text-green-700 transition-colors p-2 rounded-full hover:bg-green-50"
                    title="Éditer"
                  >
                    <FaEdit className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentsListPage;

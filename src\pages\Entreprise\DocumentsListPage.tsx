import React, { useState } from 'react';
import { FaChevronDown, FaChevronUp, FaUpload, FaDownload } from 'react-icons/fa';

// Mock data for documents organized by sections
const mockDocumentSections = [
  {
    id: 1,
    title: 'Documents Juridiques',
    documents: [
      {
        id: 1,
        name: 'Certificat Négatif',
        hasSubSection: true,
        subSection: {
          fields: [
            { id: 'nomination', label: 'Nomination', type: 'text', value: '' },
            { id: 'ice', label: 'ICE', type: 'text', value: '' }
          ],
          upload: {
            id: 'upload_certificat',
            label: 'Upload',
            acceptedTypes: '.pdf,.doc,.docx'
          }
        }
      },
      {
        id: 2,
        name: 'Dossier Juridiques',
        hasSubSection: true,
        subSection: {
          downloadFiles: [
            { id: 'statuts', name: 'Statuts', fileName: 'statuts.pdf', url: '/documents/statuts.pdf' },
            { id: 'domiciliation', name: 'Domiciliation / Contrat de Bail', fileName: 'domiciliation.pdf', url: '/documents/domiciliation.pdf' },
            { id: 'rc_model_2', name: 'RC Model 2', fileName: 'rc_model_2.pdf', url: '/documents/rc_model_2.pdf' },
            { id: 'demande_patente', name: 'Demande Patente', fileName: 'demande_patente.pdf', url: '/documents/demande_patente.pdf' },
            { id: 'depot', name: 'Dépôt', fileName: 'depot.pdf', url: '/documents/depot.pdf' },
            { id: 'declaration_honneur', name: 'Déclaration sur l\'honneur', fileName: 'declaration_honneur.pdf', url: '/documents/declaration_honneur.pdf' }
          ]
        }
      },
      {
        id: 3,
        name: 'Enregistrement Dossier',
        hasSubSection: true,
        subSection: {
          uploadFiles: [
            { id: 'eng_statut', label: 'ENG Statut', acceptedTypes: '.pdf,.doc,.docx', required: true },
            { id: 'eng_contrat', label: 'ENG Contrat', acceptedTypes: '.pdf,.doc,.docx', required: true }
          ]
        }
      },
    ]
  },
  {
    id: 2,
    title: 'Documents Commerciaux',
    documents: [
      { id: 4, name: 'Patente', hasSubSection: false },
      { id: 5, name: 'Registre de commerce', hasSubSection: false },
      { id: 6, name: 'RC Model 7', hasSubSection: false },
    ]
  },
  {
    id: 3,
    title: 'Documents Fiscaux',
    documents: [
      { id: 7, name: 'Identifiant Fiscal', hasSubSection: false },
      { id: 8, name: 'Code Sample', hasSubSection: false },
    ]
  },
  {
    id: 4,
    title: 'Documents CNSS',
    documents: [
      { id: 9, name: 'Demande CNSS', hasSubSection: false },
      { id: 10, name: 'Attestation CNSS', hasSubSection: false },
    ]
  },
];

interface DocumentsListPageProps {
  orderId?: number;
  orderReference?: string;
  onClose?: () => void;
}

const DocumentsListPage: React.FC<DocumentsListPageProps> = ({
  orderId,
  orderReference,
  onClose
}) => {
  // State to manage which sections are expanded
  const [expandedSections, setExpandedSections] = useState<Set<number>>(new Set());
  // State to manage which document sub-sections are expanded
  const [expandedDocuments, setExpandedDocuments] = useState<Set<number>>(new Set());
  // State to manage form values
  const [formValues, setFormValues] = useState<Record<string, string>>({});

  // Handle toggle section
  const handleToggleSection = (sectionId: number) => {
    const newExpandedSections = new Set(expandedSections);
    if (newExpandedSections.has(sectionId)) {
      newExpandedSections.delete(sectionId);
    } else {
      newExpandedSections.add(sectionId);
    }
    setExpandedSections(newExpandedSections);
  };

  // Handle toggle document sub-section
  const handleToggleDocument = (documentId: number) => {
    const newExpandedDocuments = new Set(expandedDocuments);
    if (newExpandedDocuments.has(documentId)) {
      newExpandedDocuments.delete(documentId);
    } else {
      newExpandedDocuments.add(documentId);
    }
    setExpandedDocuments(newExpandedDocuments);
  };

  // Handle form field change
  const handleFieldChange = (fieldId: string, value: string) => {
    setFormValues(prev => ({
      ...prev,
      [fieldId]: value
    }));
  };

  // Handle file upload
  const handleFileUpload = (uploadId: string, file: File) => {
    console.log('Upload file for:', uploadId, file);
    // Implement file upload functionality
  };

  // Handle file download
  const handleFileDownload = (url: string, fileName: string) => {
    console.log('Download file:', fileName, 'from:', url);
    // Implement file download functionality
    // For now, we'll just open the URL in a new tab
    window.open(url, '_blank');
  };

  return (
    <div className="bg-white p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Documents Juridiques</h2>
          {orderReference && (
            <p className="text-gray-600">
              Dossier: {orderReference} {orderId && `(ID: ${orderId})`}
            </p>
          )}
        </div>

        {/* Document Sections */}
        <div className="space-y-6">
          {mockDocumentSections.map((section) => {
            const isExpanded = expandedSections.has(section.id);

            return (
              <div key={section.id} className="bg-white rounded-lg shadow-md border border-gray-200">
                {/* Section Header */}
                <div
                  className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50 rounded-t-lg cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleToggleSection(section.id)}
                >
                  <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                  <div className="text-blue-500 transition-colors">
                    {isExpanded ? (
                      <FaChevronUp className="w-4 h-4" />
                    ) : (
                      <FaChevronDown className="w-4 h-4" />
                    )}
                  </div>
                </div>

                {/* Section Content */}
                {isExpanded && (
                  <div className="p-4">
                    <div className="space-y-3">
                      {section.documents.map((document) => {
                        const isDocumentExpanded = expandedDocuments.has(document.id);

                        return (
                          <div key={document.id} className="border border-gray-200 rounded-lg">
                            {/* Document Header */}
                            <div
                              className={`flex items-center justify-between p-3 transition-colors ${
                                document.hasSubSection
                                  ? 'cursor-pointer hover:bg-gray-50'
                                  : 'bg-white'
                              }`}
                              onClick={document.hasSubSection ? () => handleToggleDocument(document.id) : undefined}
                            >
                              <div className="flex items-center space-x-3">
                                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                  <span className="text-blue-600 text-sm font-medium">📄</span>
                                </div>
                                <span className="text-sm font-medium text-gray-900">{document.name}</span>
                              </div>

                              {document.hasSubSection && (
                                <div className="text-blue-500 transition-colors">
                                  {isDocumentExpanded ? (
                                    <FaChevronUp className="w-3 h-3" />
                                  ) : (
                                    <FaChevronDown className="w-3 h-3" />
                                  )}
                                </div>
                              )}
                            </div>

                            {/* Document Sub-Section */}
                            {document.hasSubSection && isDocumentExpanded && document.subSection && (
                              <div className="border-t border-gray-200 p-4 bg-gray-50">
                                <div className="space-y-4">
                                  {/* Form Fields (for Certificat Négatif) */}
                                  {document.subSection.fields && (
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                      {document.subSection.fields.map((field) => (
                                        <div key={field.id} className="space-y-1">
                                          <label className="block text-sm font-medium text-gray-700">
                                            {field.label}
                                          </label>
                                          <input
                                            type={field.type}
                                            value={formValues[field.id] || field.value}
                                            onChange={(e) => handleFieldChange(field.id, e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            placeholder={`Entrez ${field.label.toLowerCase()}`}
                                          />
                                        </div>
                                      ))}
                                    </div>
                                  )}

                                  {/* Upload Section (for Certificat Négatif) */}
                                  {document.subSection.upload && (
                                    <div className="space-y-2">
                                      <label className="block text-sm font-medium text-gray-700">
                                        {document.subSection.upload.label}
                                      </label>
                                      <div className="flex items-center space-x-3">
                                        <input
                                          type="file"
                                          accept={document.subSection.upload.acceptedTypes}
                                          onChange={(e) => {
                                            const file = e.target.files?.[0];
                                            if (file) {
                                              handleFileUpload(document.subSection!.upload!.id, file);
                                            }
                                          }}
                                          className="hidden"
                                          id={`file-${document.subSection.upload.id}`}
                                        />
                                        <label
                                          htmlFor={`file-${document.subSection.upload.id}`}
                                          className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 cursor-pointer transition-colors"
                                        >
                                          <FaUpload className="w-4 h-4" />
                                          <span>Choisir un fichier</span>
                                        </label>
                                        <span className="text-sm text-gray-500">
                                          Formats acceptés: {document.subSection.upload.acceptedTypes}
                                        </span>
                                      </div>
                                    </div>
                                  )}

                                  {/* Download Files Section (for Dossier Juridiques) */}
                                  {document.subSection.downloadFiles && (
                                    <div className="space-y-3">
                                      <h4 className="text-md font-semibold text-gray-800 mb-3">Fichiers disponibles au téléchargement</h4>
                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                        {document.subSection.downloadFiles.map((file) => (
                                          <div key={file.id} className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                            <div className="flex items-center space-x-3">
                                              <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                                <span className="text-red-600 text-sm font-medium">📄</span>
                                              </div>
                                              <div>
                                                <span className="text-sm font-medium text-gray-900">{file.name}</span>
                                                <p className="text-xs text-gray-500">{file.fileName}</p>
                                              </div>
                                            </div>
                                            <button
                                              onClick={() => handleFileDownload(file.url, file.fileName)}
                                              className="flex items-center space-x-1 px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors text-sm"
                                              title={`Télécharger ${file.name}`}
                                            >
                                              <FaDownload className="w-3 h-3" />
                                              <span>Télécharger</span>
                                            </button>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}

                                  {/* Upload Files Section (for Enregistrement Dossier) */}
                                  {document.subSection.uploadFiles && (
                                    <div className="space-y-3">
                                      <h4 className="text-md font-semibold text-gray-800 mb-3">Fichiers à téléverser</h4>
                                      <div className="space-y-4">
                                        {document.subSection.uploadFiles.map((uploadFile) => (
                                          <div key={uploadFile.id} className="p-4 bg-white border border-gray-200 rounded-lg">
                                            <div className="space-y-3">
                                              <div className="flex items-center space-x-2">
                                                <label className="block text-sm font-medium text-gray-700">
                                                  {uploadFile.label}
                                                </label>
                                                {uploadFile.required && (
                                                  <span className="text-red-500 text-sm">*</span>
                                                )}
                                              </div>
                                              <div className="flex items-center space-x-3">
                                                <input
                                                  type="file"
                                                  accept={uploadFile.acceptedTypes}
                                                  onChange={(e) => {
                                                    const file = e.target.files?.[0];
                                                    if (file) {
                                                      handleFileUpload(uploadFile.id, file);
                                                    }
                                                  }}
                                                  className="hidden"
                                                  id={`file-${uploadFile.id}`}
                                                />
                                                <label
                                                  htmlFor={`file-${uploadFile.id}`}
                                                  className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 cursor-pointer transition-colors"
                                                >
                                                  <FaUpload className="w-4 h-4" />
                                                  <span>Choisir un fichier</span>
                                                </label>
                                                <span className="text-sm text-gray-500">
                                                  Formats acceptés: {uploadFile.acceptedTypes}
                                                </span>
                                              </div>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default DocumentsListPage;

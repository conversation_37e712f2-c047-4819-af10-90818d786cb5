import React from 'react';

const WorkingDashboardEnterprise = () => {
  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f8fafc', 
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* Header */}
        <div style={{ 
          backgroundColor: 'white', 
          padding: '20px', 
          borderRadius: '8px', 
          marginBottom: '20px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h1 style={{ 
            fontSize: '28px', 
            fontWeight: 'bold', 
            color: '#1a202c', 
            margin: '0 0 10px 0' 
          }}>
            Dashboard Entreprise
          </h1>
          <p style={{ color: '#718096', margin: 0 }}>
            Gérez vos services et clients efficacement
          </p>
        </div>

        {/* Stats Cards */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
          gap: '20px', 
          marginBottom: '20px' 
        }}>
          <div style={{ 
            backgroundColor: 'white', 
            padding: '20px', 
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{ color: '#4a5568', fontSize: '14px', margin: '0 0 10px 0' }}>Clients Actifs</h3>
            <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#3182ce', margin: '0 0 5px 0' }}>156</p>
            <p style={{ fontSize: '14px', color: '#38a169', margin: 0 }}>+12.5%</p>
          </div>

          <div style={{ 
            backgroundColor: 'white', 
            padding: '20px', 
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{ color: '#4a5568', fontSize: '14px', margin: '0 0 10px 0' }}>Domiciliations</h3>
            <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#38a169', margin: '0 0 5px 0' }}>89</p>
            <p style={{ fontSize: '14px', color: '#38a169', margin: 0 }}>+8</p>
          </div>

          <div style={{ 
            backgroundColor: 'white', 
            padding: '20px', 
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{ color: '#4a5568', fontSize: '14px', margin: '0 0 10px 0' }}>Services Juridiques</h3>
            <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#805ad5', margin: '0 0 5px 0' }}>34</p>
            <p style={{ fontSize: '14px', color: '#38a169', margin: 0 }}>+5</p>
          </div>

          <div style={{ 
            backgroundColor: 'white', 
            padding: '20px', 
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{ color: '#4a5568', fontSize: '14px', margin: '0 0 10px 0' }}>Chiffre d'Affaires</h3>
            <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#ed8936', margin: '0 0 5px 0' }}>245K MAD</p>
            <p style={{ fontSize: '14px', color: '#38a169', margin: 0 }}>+15.2%</p>
          </div>
        </div>

        {/* Actions rapides */}
        <div style={{ 
          backgroundColor: 'white', 
          padding: '20px', 
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          marginBottom: '20px'
        }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', color: '#1a202c', marginBottom: '15px' }}>
            Actions rapides
          </h2>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '15px' 
          }}>
            <button 
              onClick={() => alert('Nouvelle Domiciliation')}
              style={{
                padding: '15px',
                border: '1px solid #e2e8f0',
                borderRadius: '8px',
                backgroundColor: 'white',
                cursor: 'pointer',
                textAlign: 'left',
                transition: 'all 0.2s'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#f7fafc'}
              onMouseOut={(e) => e.target.style.backgroundColor = 'white'}
            >
              <h3 style={{ fontWeight: '500', color: '#1a202c', margin: '0 0 5px 0' }}>
                Nouvelle Domiciliation
              </h3>
              <p style={{ fontSize: '14px', color: '#718096', margin: 0 }}>
                Créer une domiciliation pour un client
              </p>
            </button>

            <button 
              onClick={() => alert('Ajouter une Offre')}
              style={{
                padding: '15px',
                border: '1px solid #e2e8f0',
                borderRadius: '8px',
                backgroundColor: 'white',
                cursor: 'pointer',
                textAlign: 'left'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#f7fafc'}
              onMouseOut={(e) => e.target.style.backgroundColor = 'white'}
            >
              <h3 style={{ fontWeight: '500', color: '#1a202c', margin: '0 0 5px 0' }}>
                Ajouter une Offre
              </h3>
              <p style={{ fontSize: '14px', color: '#718096', margin: 0 }}>
                Créer une nouvelle offre de service
              </p>
            </button>

            <button 
              onClick={() => alert('Gestion Clients')}
              style={{
                padding: '15px',
                border: '1px solid #e2e8f0',
                borderRadius: '8px',
                backgroundColor: 'white',
                cursor: 'pointer',
                textAlign: 'left'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#f7fafc'}
              onMouseOut={(e) => e.target.style.backgroundColor = 'white'}
            >
              <h3 style={{ fontWeight: '500', color: '#1a202c', margin: '0 0 5px 0' }}>
                Gestion Clients
              </h3>
              <p style={{ fontSize: '14px', color: '#718096', margin: 0 }}>
                Voir et gérer les clients
              </p>
            </button>

            <button 
              onClick={() => alert('Rapports')}
              style={{
                padding: '15px',
                border: '1px solid #e2e8f0',
                borderRadius: '8px',
                backgroundColor: 'white',
                cursor: 'pointer',
                textAlign: 'left'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#f7fafc'}
              onMouseOut={(e) => e.target.style.backgroundColor = 'white'}
            >
              <h3 style={{ fontWeight: '500', color: '#1a202c', margin: '0 0 5px 0' }}>
                Rapports
              </h3>
              <p style={{ fontSize: '14px', color: '#718096', margin: 0 }}>
                Générer des rapports d'activité
              </p>
            </button>
          </div>
        </div>

        {/* Activités récentes */}
        <div style={{ 
          backgroundColor: 'white', 
          padding: '20px', 
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', color: '#1a202c', marginBottom: '15px' }}>
            Activités récentes
          </h2>
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ borderBottom: '1px solid #e2e8f0' }}>
                  <th style={{ textAlign: 'left', padding: '10px', color: '#718096', fontWeight: '500' }}>Type</th>
                  <th style={{ textAlign: 'left', padding: '10px', color: '#718096', fontWeight: '500' }}>Client</th>
                  <th style={{ textAlign: 'left', padding: '10px', color: '#718096', fontWeight: '500' }}>Statut</th>
                  <th style={{ textAlign: 'left', padding: '10px', color: '#718096', fontWeight: '500' }}>Date</th>
                  <th style={{ textAlign: 'left', padding: '10px', color: '#718096', fontWeight: '500' }}>Montant</th>
                </tr>
              </thead>
              <tbody>
                <tr style={{ borderBottom: '1px solid #f7fafc' }}>
                  <td style={{ padding: '10px', color: '#1a202c' }}>Nouvelle Domiciliation</td>
                  <td style={{ padding: '10px', color: '#1a202c' }}>SARL TECH INNOVATIONS</td>
                  <td style={{ padding: '10px' }}>
                    <span style={{ 
                      backgroundColor: '#fef5e7', 
                      color: '#744210', 
                      padding: '4px 8px', 
                      borderRadius: '12px', 
                      fontSize: '12px' 
                    }}>
                      En cours
                    </span>
                  </td>
                  <td style={{ padding: '10px', color: '#718096' }}>2024-01-15</td>
                  <td style={{ padding: '10px', color: '#1a202c', fontWeight: '500' }}>2,500 MAD</td>
                </tr>
                <tr style={{ borderBottom: '1px solid #f7fafc' }}>
                  <td style={{ padding: '10px', color: '#1a202c' }}>Service Juridique</td>
                  <td style={{ padding: '10px', color: '#1a202c' }}>SAS DIGITAL SOLUTIONS</td>
                  <td style={{ padding: '10px' }}>
                    <span style={{ 
                      backgroundColor: '#f0fff4', 
                      color: '#22543d', 
                      padding: '4px 8px', 
                      borderRadius: '12px', 
                      fontSize: '12px' 
                    }}>
                      Terminé
                    </span>
                  </td>
                  <td style={{ padding: '10px', color: '#718096' }}>2024-01-14</td>
                  <td style={{ padding: '10px', color: '#1a202c', fontWeight: '500' }}>5,000 MAD</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkingDashboardEnterprise;

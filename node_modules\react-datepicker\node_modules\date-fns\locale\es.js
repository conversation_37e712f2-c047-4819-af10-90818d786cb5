import { formatDistance } from "./es/_lib/formatDistance.js";
import { formatLong } from "./es/_lib/formatLong.js";
import { formatRelative } from "./es/_lib/formatRelative.js";
import { localize } from "./es/_lib/localize.js";
import { match } from "./es/_lib/match.js";

/**
 * @category Locales
 * @summary Spanish locale.
 * @language Spanish
 * @iso-639-2 spa
 * <AUTHOR> [@juanangosto](https://github.com/juanangosto)
 * <AUTHOR> [@guigrpa](https://github.com/guigrpa)
 * <AUTHOR> [@fjaguero](https://github.com/fjaguero)
 * <AUTHOR> [@harogaston](https://github.com/harogaston)
 * <AUTHOR> [@YagoCarballo](https://github.com/YagoCarballo)
 */
export const es = {
  code: "es",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default es;

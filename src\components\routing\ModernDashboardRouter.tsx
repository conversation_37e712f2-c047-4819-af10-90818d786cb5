import React from 'react';
import { useAuthContext } from '../../contexts/AuthContext';
import ModernDashboardClientPage from '../../pages/Client/ModernDashboardClientPage';
import ModernDashboardEnterprisePage from '../../pages/Enterprise/ModernDashboardEnterprisePage';
import ModernDashboardCourierPage from '../../pages/Courier/ModernDashboardCourierPage';

const ModernDashboardRouter: React.FC = () => {
  const { user } = useAuthContext();

  // Déterminer le type d'utilisateur
  const getUserType = () => {
    if (!user) return 'client'; // Par défaut
    
    // Logique pour déterminer le type d'utilisateur
    // Vous pouvez adapter cette logique selon votre structure de données
    if (user.role === 'enterprise' || user.type === 'enterprise') {
      return 'enterprise';
    } else if (user.role === 'courier' || user.type === 'courier') {
      return 'courier';
    } else {
      return 'client';
    }
  };

  const userType = getUserType();

  // Rendre le dashboard approprié selon le type d'utilisateur
  switch (userType) {
    case 'enterprise':
      return <ModernDashboardEnterprisePage />;
    case 'courier':
      return <ModernDashboardCourierPage />;
    case 'client':
    default:
      return <ModernDashboardClientPage />;
  }
};

export default ModernDashboardRouter;

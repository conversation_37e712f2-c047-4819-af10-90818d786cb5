export const THEME_ENUM = {
  DIR_LTR: 'ltr',
  DIR_RTL: 'rtl',
  MODE_LIGHT: 'light',
  MODE_DARK: 'dark',
  NAV_MODE_LIGHT: 'light',
  NAV_MODE_DARK: 'dark',
  NAV_MODE_THEMED: 'themed',
  NAV_MODE_TRANSPARENT: 'transparent',
  LAYOUT_TYPE_CLASSIC: 'classic',
  LAYOUT_TYPE_MODERN: 'modern',
  LAYOUT_TYPE_STACKED_SIDE: 'stackedSide',
  LAYOUT_TYPE_SIMPLE: 'simple',
  LAYOUT_TYPE_DECKED: 'decked',
  LAYOUT_TYPE_BLANK: 'blank',
} as const;

export type Direction = 'ltr' | 'rtl';
export type Mode = 'light' | 'dark';
export type NavMode = 'light' | 'dark' | 'themed' | 'transparent';
export type LayoutType = 'classic' | 'modern' | 'stackedSide' | 'simple' | 'decked' | 'blank';
export type ColorLevel = 50 | 100 | 200 | 300 | 400 | 500 | 600 | 700 | 800 | 900;
export type ControlSize = 'sm' | 'md' | 'lg';

export type ThemeConfig = {
  themeColor: string;
  direction: Direction;
  mode: Mode;
  primaryColorLevel: ColorLevel;
  panelExpand: boolean;
  navMode: NavMode;
  controlSize: ControlSize;
  cardBordered: boolean;
  layout: {
    type: LayoutType;
    sideNavCollapse: boolean;
  };
};

export const themeConfig: ThemeConfig = {
  themeColor: 'indigo',
  direction: THEME_ENUM.DIR_LTR,
  mode: THEME_ENUM.MODE_LIGHT,
  primaryColorLevel: 600,
  cardBordered: true,
  panelExpand: false,
  controlSize: 'md',
  navMode: THEME_ENUM.NAV_MODE_LIGHT,
  layout: {
    type: THEME_ENUM.LAYOUT_TYPE_MODERN,
    sideNavCollapse: false,
  },
};

// Constantes pour les dimensions
export const SIDE_NAV_WIDTH = 280;
export const SIDE_NAV_COLLAPSED_WIDTH = 80;
export const SIDE_NAV_CONTENT_GUTTER = 'px-4';
export const LOGO_X_GUTTER = 'px-6';
export const HEADER_HEIGHT = 60;

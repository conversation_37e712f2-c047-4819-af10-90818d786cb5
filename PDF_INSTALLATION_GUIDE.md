# Guide d'installation pour la génération PDF

## 📋 Problème
L'erreur `Failed to resolve import "@react-pdf/renderer"` indique que la bibliothèque n'est pas installée.

## 🔧 Solution

### Étape 1 : Installation de la bibliothèque
Exécutez l'une des commandes suivantes dans le terminal :

```bash
# Avec npm
npm install @react-pdf/renderer

# Avec yarn
yarn add @react-pdf/renderer

# Avec pnpm
pnpm add @react-pdf/renderer
```

### Étape 2 : Fonctionnalité PDF activée
✅ **La génération PDF est maintenant intégrée dans le formulaire de domiciliation entreprise** !

## 📄 Fonctionnalités PDF disponibles

Le système génère automatiquement une **Attestation de Domiciliation** PDF contenant :

- ✅ **Logo de l'entreprise** MACHROUHI AFFAIRES
- ✅ **Informations du gérant** (nom, CIN, adresse)
- ✅ **Informations de l'entreprise** (nom, ICE, dates)
- ✅ **Attestation officielle** de domiciliation
- ✅ **Clauses légales** conformes au décret N°2.20.950
- ✅ **Contrat de domiciliation** intégré
- ✅ **Date de génération** automatique

## 🎯 Utilisation

### Formulaire de Domiciliation (Section Entreprise) :
1. **Accéder** à la section entreprise → Domiciliation
2. **Cliquer** sur "Créer une domiciliation"
3. **Remplir** les informations du gérant, entreprise et dates
4. **Cliquer** sur "Créer la domiciliation"
5. **PDF généré automatiquement** et téléchargé
6. **Bouton de re-téléchargement** dans le message de succès

### Formulaire Juridique Client :
- ✅ **Formulaire fonctionnel** sans génération PDF
- ✅ **Message de succès** standard
- ✅ **Pas de dépendance** @react-pdf/renderer

## 🔄 État actuel

- ✅ **Domiciliation PDF** : Fonctionnel et intégré
- ✅ **Formulaire juridique client** : Fonctionnel sans PDF
- ✅ **Composant DomiciliationPdf** : Créé et opérationnel
- ✅ **Interface utilisateur** : Complète avec gestion des états

## 📝 Fichiers modifiés

- ✅ `src/components/forms/DomiciliationMainForm.tsx` : PDF intégré
- ✅ `src/components/domiciliation/DomiciliationPdf.tsx` : Nouveau composant
- ✅ `src/components/forms/ClientJuridicalServiceForm.tsx` : Nettoyé
- ❌ `src/components/juridical/JuridicalServicePdf.tsx` : Supprimé

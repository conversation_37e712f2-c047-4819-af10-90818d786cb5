import React, { useState } from 'react';
import { FaArrowLeft, FaUpload, FaSearch, FaDownload } from 'react-icons/fa';
import { PDFDownloadLink } from '@react-pdf/renderer';
import JuridicalServicePdf from '../juridical/JuridicalServicePdf';

interface ClientJuridicalServiceFormProps {
  onClose?: () => void;
}

const ClientJuridicalServiceForm: React.FC<ClientJuridicalServiceFormProps> = ({ onClose }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [showSuccess, setShowSuccess] = useState(false);
  const [formData, setFormData] = useState({
    selectedService: '',
    nomComplet: '',
    cin: '',
    adresse: '',
    villeCreation: '',
    typeSociete: '',
    objetsSociete: '',
    carteNationale: null as File | null,
    nom1: '',
    nom2: '',
    nom3: '',
    capitale: '',
    signature: '',
    associes: [] as Array<{
      nom: string;
      cin: string;
      adresse: string;
      type: string;
      partSociale: string;
    }>,
    newAssocie: {
      nom: '',
      cin: '',
      adresse: '',
      type: '',
      partSociale: ''
    },
    contratType: '',
    siegeSocial: '',
    searchQuery: '',
    selectedOffer: null as number | null
  });

  const totalSteps = 5;

  // Services juridiques disponibles
  const juridicalServices = [
    { id: 'creation-societe', name: 'Création de société', icon: '🏢' },
    { id: 'modification-statuts', name: 'Modification des statuts', icon: '📝' },
    { id: 'transfert-siege', name: 'Transfert de siège social', icon: '🏠' },
    { id: 'changement-dirigeant', name: 'Changement de dirigeant', icon: '👤' },
    { id: 'changement-denomination', name: 'Changement de dénomination sociale', icon: '🏷️' },
    { id: 'modification-objet', name: 'Modification de l\'objet social', icon: '🎯' },
    { id: 'augmentation-capital', name: 'Augmentation de capital', icon: '📈' },
    { id: 'reduction-capital', name: 'Réduction de capital', icon: '📉' },
    { id: 'cession-parts', name: 'Cession de parts sociales ou d\'actions', icon: '🔄' },
    { id: 'entree-sortie-associe', name: 'Entrée ou sortie d\'un associé', icon: '👥' }
  ];

  // Mock offers data
  const offers = [
    {
      id: 1,
      icon: '🌿',
      name: 'Nom d\'entreprise',
      address: 'Adress',
      domiciliation: '3000DH',
      creation: '3000DH'
    },
    {
      id: 2,
      icon: '🔥',
      name: 'Nom d\'entreprise',
      address: 'Adress',
      domiciliation: '2000DH',
      creation: '2000DH'
    },
    {
      id: 3,
      icon: '👤',
      name: 'Nom d\'entreprise',
      address: 'Adress',
      domiciliation: '2000DH',
      creation: '2000DH'
    }
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFileUpload = (file: File | null) => {
    setFormData(prev => ({
      ...prev,
      carteNationale: file
    }));
  };

  const addAssocie = () => {
    if (formData.newAssocie.nom && formData.newAssocie.cin && formData.newAssocie.adresse && formData.newAssocie.type && formData.newAssocie.partSociale) {
      setFormData(prev => ({
        ...prev,
        associes: [...prev.associes, prev.newAssocie],
        newAssocie: {
          nom: '',
          cin: '',
          adresse: '',
          type: '',
          partSociale: ''
        }
      }));
    }
  };

  const removeAssocie = (index: number) => {
    setFormData(prev => ({
      ...prev,
      associes: prev.associes.filter((_, i) => i !== index)
    }));
  };

  const handleOfferSelect = (offerId: number) => {
    setFormData(prev => ({
      ...prev,
      selectedOffer: offerId
    }));
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleServiceSelect = (serviceId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedService: serviceId
    }));
  };

  const handleSubmit = () => {
    // Handle form submission
    console.log('Form submitted:', formData);

    // Générer automatiquement le PDF
    generatePDF();

    // Afficher le message de succès au lieu de fermer directement
    setShowSuccess(true);
  };

  const generatePDF = () => {
    // Créer un élément temporaire pour déclencher le téléchargement
    const link = document.createElement('a');
    link.style.display = 'none';
    document.body.appendChild(link);

    // Déclencher le téléchargement du PDF
    setTimeout(() => {
      const downloadButton = document.querySelector('#pdf-download-link') as HTMLAnchorElement;
      if (downloadButton) {
        downloadButton.click();
      }
      document.body.removeChild(link);
    }, 100);
  };

  const renderStepIndicator = () => {
    // Ne pas afficher l'indicateur d'étapes pour l'étape de présélection
    if (currentStep === 0) {
      return null;
    }

    return (
      <div className="flex items-center justify-center mb-8">
        {[1, 2, 3, 4].map((step) => (
          <React.Fragment key={step}>
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                step <= currentStep
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-500'
              }`}
            >
              {step}
            </div>
            {step < 4 && (
              <div
                className={`w-20 h-1 mx-3 ${
                  step < currentStep ? 'bg-blue-500' : 'bg-gray-200'
                }`}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    );
  };

  const renderStep0 = () => (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold text-gray-900 mb-4">Choisissez votre service juridique</h3>
        <p className="text-gray-600">Sélectionnez le type de service juridique dont vous avez besoin</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {juridicalServices.map((service) => (
          <div
            key={service.id}
            onClick={() => handleServiceSelect(service.id)}
            className={`p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:shadow-lg ${
              formData.selectedService === service.id
                ? 'border-blue-500 bg-blue-50 shadow-md'
                : 'border-gray-200 bg-white hover:border-blue-300'
            }`}
          >
            <div className="text-center">
              <div className="text-4xl mb-4">{service.icon}</div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">{service.name}</h4>
              <div className={`w-full h-1 rounded-full ${
                formData.selectedService === service.id ? 'bg-blue-500' : 'bg-gray-200'
              }`}></div>
            </div>
          </div>
        ))}
      </div>

      {formData.selectedService && (
        <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center">
            <div className="text-blue-500 mr-3">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <p className="text-blue-700 font-medium">
              Service sélectionné : {juridicalServices.find(s => s.id === formData.selectedService)?.name}
            </p>
          </div>
        </div>
      )}
    </div>
  );

  const renderStep1 = () => (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="flex items-center mb-6">
        <button
          onClick={handlePrevious}
          className="mr-4 p-2 text-gray-500 hover:text-gray-700"
          disabled={currentStep === 1}
        >
          <FaArrowLeft />
        </button>
        <h3 className="text-xl font-semibold text-blue-500">Les informations</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label className="block text-xs font-semibold text-gray-800 mb-2 uppercase tracking-wider">
            NOM COMPLET
          </label>
          <input
            type="text"
            value={formData.nomComplet}
            onChange={(e) => handleInputChange('nomComplet', e.target.value)}
            placeholder="EZZIANI Youssef"
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
          />
        </div>
        <div>
          <label className="block text-xs font-semibold text-gray-800 mb-2 uppercase tracking-wider">
            CIN
          </label>
          <input
            type="text"
            value={formData.cin}
            onChange={(e) => handleInputChange('cin', e.target.value)}
            placeholder="AB123456"
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Ville de création
          </label>
          <input
            type="text"
            value={formData.villeCreation}
            onChange={(e) => handleInputChange('villeCreation', e.target.value)}
            placeholder="Casablanca"
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Adresse
          </label>
          <input
            type="text"
            value={formData.adresse}
            onChange={(e) => handleInputChange('adresse', e.target.value)}
            placeholder="Ain Sbaa, Casablanca, Maroc"
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Forme Juridique
          </label>
          <select
            value={formData.typeSociete}
            onChange={(e) => handleInputChange('typeSociete', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700 appearance-none"
          >
            <option value="">Sélectionner...</option>
            <option value="SARL">SARL</option>
            <option value="SA">SA</option>
            <option value="SNC">SNC</option>
            <option value="Auto-entrepreneur">Auto-entrepreneur</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Objets
          </label>
          <select
            value={formData.objetsSociete}
            onChange={(e) => handleInputChange('objetsSociete', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700 appearance-none"
          >
            <option value="">Sélectionner...</option>
            <option value="Commerce">Commerce</option>
            <option value="Services">Services</option>
            <option value="Industrie">Industrie</option>
            <option value="Artisanat">Artisanat</option>
            <option value="Agriculture">Agriculture</option>
          </select>
        </div>
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          CIN
        </label>
        <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center bg-gray-50">
          <input
            type="file"
            id="carte-nationale-client"
            className="hidden"
            accept="image/*,.pdf"
            onChange={(e) => handleFileUpload(e.target.files?.[0] || null)}
          />
          <label
            htmlFor="carte-nationale-client"
            className="cursor-pointer inline-flex items-center px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 font-medium shadow-sm"
          >
            <FaUpload className="mr-2" />
            Upload
          </label>
          {formData.carteNationale && (
            <p className="mt-3 text-sm text-gray-600">
              Fichier sélectionné: {formData.carteNationale.name}
            </p>
          )}
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="flex items-center mb-6">
        <button
          onClick={handlePrevious}
          className="mr-4 p-2 text-gray-500 hover:text-gray-700"
        >
          <FaArrowLeft />
        </button>
        <h3 className="text-xl font-semibold text-blue-500">Certificat négatif 1</h3>
      </div>

      <div className="mb-6">
        <h4 className="text-lg font-medium text-gray-800 mb-6">
          Choisissez 3 Noms pour votre entreprise
        </h4>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom 1
            </label>
            <input
              type="text"
              value={formData.nom1}
              onChange={(e) => handleInputChange('nom1', e.target.value)}
              placeholder="Nom 1"
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom 2
            </label>
            <input
              type="text"
              value={formData.nom2}
              onChange={(e) => handleInputChange('nom2', e.target.value)}
              placeholder="Nom 2"
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom 3
            </label>
            <input
              type="text"
              value={formData.nom3}
              onChange={(e) => handleInputChange('nom3', e.target.value)}
              placeholder="Nom 3"
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
            />
          </div>
        </div>

        <div className="mt-8 p-6 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-700">
            En va déclarer le nom dans une période de 24h/48h
          </p>
        </div>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="flex items-center mb-6">
        <button
          onClick={handlePrevious}
          className="mr-4 p-2 text-gray-500 hover:text-gray-700"
        >
          <FaArrowLeft />
        </button>
        <h3 className="text-xl font-semibold text-blue-500">Associés</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Capitale
          </label>
          <input
            type="text"
            value={formData.capitale}
            onChange={(e) => handleInputChange('capitale', e.target.value)}
            placeholder="100 000 DH"
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Signature
          </label>
          <select
            value={formData.signature}
            onChange={(e) => handleInputChange('signature', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700 appearance-none"
          >
            <option value="">Sélectionner...</option>
            <option value="ET">ET</option>
            <option value="OU">OU</option>
          </select>
        </div>
      </div>

      {/* Formulaire d'ajout d'associé */}
      <div className="bg-blue-50 rounded-lg p-6 mb-6">
        <div className="flex items-center mb-6">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
            <span className="text-white text-sm">👥</span>
          </div>
          <h4 className="text-lg font-medium text-blue-600">Associés</h4>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom complet
            </label>
            <input
              type="text"
              value={formData.newAssocie?.nom || ''}
              onChange={(e) => handleInputChange('newAssocie', { ...formData.newAssocie, nom: e.target.value })}
              placeholder="Nom complet"
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-700"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CIN
            </label>
            <input
              type="text"
              value={formData.newAssocie?.cin || ''}
              onChange={(e) => handleInputChange('newAssocie', { ...formData.newAssocie, cin: e.target.value })}
              placeholder="CIN"
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-700"
            />
          </div>
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Adresse
          </label>
          <input
            type="text"
            value={formData.newAssocie?.adresse || ''}
            onChange={(e) => handleInputChange('newAssocie', { ...formData.newAssocie, adresse: e.target.value })}
            placeholder="Adresse"
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-700"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Part sociale
            </label>
            <input
              type="text"
              value={formData.newAssocie?.partSociale || ''}
              onChange={(e) => handleInputChange('newAssocie', { ...formData.newAssocie, partSociale: e.target.value })}
              placeholder="Part sociale"
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-700"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type associé
            </label>
            <select
              value={formData.newAssocie?.type || ''}
              onChange={(e) => handleInputChange('newAssocie', { ...formData.newAssocie, type: e.target.value })}
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-700 appearance-none"
            >
              <option value="">Sélectionner...</option>
              <option value="Gérant">Gérant</option>
              <option value="Associé">Associé</option>
            </select>
          </div>
        </div>

        <button
          onClick={addAssocie}
          className="inline-flex items-center px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium text-sm"
        >
          <span className="mr-2">+</span>
          Ajouter à la liste
        </button>
      </div>

      {/* Table des associés */}
      <div className="mb-6">
        <h4 className="text-md font-medium text-gray-700 mb-4">Liste des associés</h4>
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                  Nom complet
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                  CIN
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                  Adresse
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                  Part sociale
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {formData.associes.map((associe, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-4 py-3 text-sm text-gray-900">{associe.nom}</td>
                  <td className="px-4 py-3 text-sm text-gray-900">{associe.cin}</td>
                  <td className="px-4 py-3 text-sm text-gray-900">{associe.adresse}</td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      associe.type === 'Gérant'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {associe.type}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">{associe.partSociale}</td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    <button
                      onClick={() => removeAssocie(index)}
                      className="text-red-600 hover:text-red-800 text-sm font-medium"
                    >
                      Supprimer
                    </button>
                  </td>
                </tr>
              ))}
              {formData.associes.length === 0 && (
                <tr>
                  <td colSpan={6} className="px-4 py-8 text-center text-gray-500 text-sm">
                    Aucun associé ajouté
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderStep4 = () => (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="flex items-center mb-6">
        <button
          onClick={handlePrevious}
          className="mr-4 p-2 text-gray-500 hover:text-gray-700"
        >
          <FaArrowLeft />
        </button>
        <h3 className="text-xl font-semibold text-blue-500">Choisissez votre contrat</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Left Column - Type de contrat */}
        <div>
          <h4 className="text-sm font-medium text-gray-500 mb-3">Type de contrat</h4>
          <div className="bg-white border border-gray-200 rounded-lg p-6 h-[160px] flex flex-col">
            <div className="space-y-4 mb-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="contratType"
                  value="bail"
                  checked={formData.contratType === 'bail'}
                  onChange={(e) => handleInputChange('contratType', e.target.value)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
                />
                <span className="ml-3 text-sm font-medium text-gray-900">Contrat de bail</span>
              </label>

              <label className="flex items-center">
                <input
                  type="radio"
                  name="contratType"
                  value="domiciliation"
                  checked={formData.contratType === 'domiciliation'}
                  onChange={(e) => handleInputChange('contratType', e.target.value)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
                />
                <span className="ml-3 text-sm font-medium text-gray-900">Contrat de Domicialisation</span>
              </label>
            </div>

            <div className="mt-auto">
              <button className="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 font-medium text-sm">
                <FaUpload className="mr-2" />
                Upload
              </button>
            </div>
          </div>
        </div>

        {/* Right Column - Siège Social */}
        <div>
          <h4 className="text-sm font-medium text-gray-500 mb-3">Siège Social</h4>
          <div className="bg-white border border-gray-200 rounded-lg p-6 h-[160px] flex flex-col justify-center">
            <textarea
              value={formData.siegeSocial}
              onChange={(e) => handleInputChange('siegeSocial', e.target.value)}
              placeholder="Adresse"
              className="w-full h-[80px] px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-700 resize-none placeholder-gray-400"
            />
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative">
          <input
            type="text"
            value={formData.searchQuery}
            onChange={(e) => handleInputChange('searchQuery', e.target.value)}
            placeholder="Trouver plus d'offres ..."
            className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-500"
          />
          <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
            <FaSearch />
          </button>
        </div>
      </div>

      {/* Meilleures Offres */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 mb-4">Meilleures Offres</h4>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {offers.map((offer) => (
            <div
              key={offer.id}
              onClick={() => handleOfferSelect(offer.id)}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                formData.selectedOffer === offer.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
            >
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 mr-3 text-lg">
                  {offer.icon}
                </div>
                <div>
                  <p className="font-medium text-sm text-gray-900">{offer.name}</p>
                  <p className="text-xs text-gray-500">{offer.address}</p>
                </div>
              </div>

              <div className="space-y-1 mb-3">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600">Domiciliation</span>
                  <span className="text-xs font-semibold text-gray-900">{offer.domiciliation}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600">Création</span>
                  <span className="text-xs font-semibold text-gray-900">{offer.creation}</span>
                </div>
              </div>

              <button className="w-full bg-blue-500 text-white py-2 rounded-md text-xs font-medium hover:bg-blue-600">
                Voir plus
              </button>
            </div>
          ))}
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-start">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              className="p-2 text-gray-400 hover:text-gray-600"
              disabled={currentPage === 1}
            >
              <FaArrowLeft />
            </button>

            {[1, 2, 3, 4].map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`w-8 h-8 rounded-full text-sm font-medium ${
                  currentPage === page
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {page}
              </button>
            ))}

            <button
              onClick={() => setCurrentPage(Math.min(4, currentPage + 1))}
              className="p-2 text-gray-400 hover:text-gray-600"
              disabled={currentPage === 4}
            >
              <FaArrowLeft className="transform rotate-180" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderStep0();
      case 1:
        return renderStep1();
      case 2:
        return renderStep2();
      case 3:
        return renderStep3();
      case 4:
        return renderStep4();
      default:
        return renderStep0();
    }
  };

  // Si le message de succès doit être affiché
  if (showSuccess) {
    return (
      <div className="bg-white p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-bold text-green-800 mb-2">Demande enregistrée avec succès</h3>
            <p className="text-green-700 mb-6">
              La demande de création de votre entreprise a bien été enregistrée. Nous attendons maintenant la validation de votre nomination par l'OMPIC, ce qui peut prendre de 24 à 48 heures.
            </p>

            {/* Bouton de téléchargement PDF visible */}
            <div className="flex justify-center space-x-4 mb-6">
              <PDFDownloadLink
                document={<JuridicalServicePdf formData={formData} />}
                fileName={`dossier-juridique-${formData.selectedService}-${new Date().toISOString().split('T')[0]}.pdf`}
                className="inline-flex items-center px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                {({ loading }: { loading: boolean }) => (
                  loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Génération...
                    </>
                  ) : (
                    <>
                      <FaDownload className="mr-2" />
                      Télécharger le dossier PDF
                    </>
                  )
                )}
              </PDFDownloadLink>
            </div>

            <button
              onClick={onClose}
              className="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              Fermer
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white">
      <div className="max-w-4xl mx-auto p-6">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Service Juridique Client</h2>
          <p className="text-gray-600">
            {currentStep === 0
              ? 'Sélectionnez votre service juridique'
              : 'Création d\'entreprise - Formulaire multi-étapes'
            }
          </p>
        </div>

        {/* PDF Download Link caché */}
        <PDFDownloadLink
          document={<JuridicalServicePdf formData={formData} />}
          fileName={`dossier-juridique-${formData.selectedService}-${new Date().toISOString().split('T')[0]}.pdf`}
          style={{ display: 'none' }}
        >
          {({ url }: { url: string | null }) => (
            <a id="pdf-download-link" href={url || '#'} style={{ display: 'none' }}>
              Télécharger PDF
            </a>
          )}
        </PDFDownloadLink>

        {renderStepIndicator()}
        {renderCurrentStep()}

        <div className="flex justify-between items-center mt-8">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 0}
            className={`px-6 py-3 rounded-lg font-medium ${
              currentStep === 0
                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                : 'bg-gray-500 text-white hover:bg-gray-600'
            }`}
          >
            Précédent
          </button>

          {currentStep === 0 ? (
            <button
              onClick={handleNext}
              disabled={!formData.selectedService}
              className={`px-6 py-3 rounded-lg font-medium ${
                !formData.selectedService
                  ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                  : 'bg-blue-500 text-white hover:bg-blue-600'
              }`}
            >
              Commencer
            </button>
          ) : currentStep < totalSteps - 1 ? (
            <button
              onClick={handleNext}
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 font-medium"
            >
              Suivant
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 font-medium"
            >
              Terminer
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClientJuridicalServiceForm;

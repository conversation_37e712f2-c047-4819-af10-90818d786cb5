{"name": "charikti-platform-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-pdf/renderer": "^4.3.0", "@tanstack/react-query": "^5.59.15", "@tanstack/react-query-devtools": "^5.59.15", "@types/react-datepicker": "^6.2.0", "axios": "^1.7.7", "dotenv": "^16.4.5", "react": "^18.3.1", "react-datepicker": "^8.3.0", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-router-dom": "^6.26.2"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "daisyui": "^4.12.10", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.12", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.9"}}
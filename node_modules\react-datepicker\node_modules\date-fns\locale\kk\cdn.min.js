(()=>{var z;function S(G,J){var X=Object.keys(G);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(G);J&&(Z=Z.filter(function(B){return Object.getOwnPropertyDescriptor(G,B).enumerable})),X.push.apply(X,Z)}return X}function I(G){for(var J=1;J<arguments.length;J++){var X=arguments[J]!=null?arguments[J]:{};J%2?S(Object(X),!0).forEach(function(Z){F(G,Z,X[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(X)):S(Object(X)).forEach(function(Z){Object.defineProperty(G,Z,Object.getOwnPropertyDescriptor(X,Z))})}return G}function F(G,J,X){if(J=w(J),J in G)Object.defineProperty(G,J,{value:X,enumerable:!0,configurable:!0,writable:!0});else G[J]=X;return G}function w(G){var J=v(G,"string");return K(J)=="symbol"?J:String(J)}function v(G,J){if(K(G)!="object"||!G)return G;var X=G[Symbol.toPrimitive];if(X!==void 0){var Z=X.call(G,J||"default");if(K(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(G)}function b(G,J){return _(G)||k(G,J)||f(G,J)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(G,J){if(!G)return;if(typeof G==="string")return W(G,J);var X=Object.prototype.toString.call(G).slice(8,-1);if(X==="Object"&&G.constructor)X=G.constructor.name;if(X==="Map"||X==="Set")return Array.from(G);if(X==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(X))return W(G,J)}function W(G,J){if(J==null||J>G.length)J=G.length;for(var X=0,Z=new Array(J);X<J;X++)Z[X]=G[X];return Z}function k(G,J){var X=G==null?null:typeof Symbol!="undefined"&&G[Symbol.iterator]||G["@@iterator"];if(X!=null){var Z,B,C,U,H=[],Y=!0,Q=!1;try{if(C=(X=X.call(G)).next,J===0){if(Object(X)!==X)return;Y=!1}else for(;!(Y=(Z=C.call(X)).done)&&(H.push(Z.value),H.length!==J);Y=!0);}catch(q){Q=!0,B=q}finally{try{if(!Y&&X.return!=null&&(U=X.return(),Object(U)!==U))return}finally{if(Q)throw B}}return H}}function _(G){if(Array.isArray(G))return G}function K(G){return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},K(G)}var g=Object.defineProperty,FG=function G(J,X){for(var Z in X)g(J,Z,{get:X[Z],enumerable:!0,configurable:!0,set:function B(C){return X[Z]=function(){return C}}})};function N(G,J){if(G.one&&J===1)return G.one;var X=J%10,Z=J%100;if(X===1&&Z!==11)return G.singularNominative.replace("{{count}}",String(J));else if(X>=2&&X<=4&&(Z<10||Z>20))return G.singularGenitive.replace("{{count}}",String(J));else return G.pluralGenitive.replace("{{count}}",String(J))}var m={lessThanXSeconds:{regular:{one:"1 \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u0430\u0437",singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u0430\u0437",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u0430\u0437",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u0430\u0437"},future:{one:"\u0431\u0456\u0440 \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}},xSeconds:{regular:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},past:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434 \u0431\u04B1\u0440\u044B\u043D",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434 \u0431\u04B1\u0440\u044B\u043D",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434 \u0431\u04B1\u0440\u044B\u043D"},future:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}},halfAMinute:function G(J){if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"\u0436\u0430\u0440\u0442\u044B \u043C\u0438\u043D\u0443\u0442 \u0456\u0448\u0456\u043D\u0434\u0435";else return"\u0436\u0430\u0440\u0442\u044B \u043C\u0438\u043D\u0443\u0442 \u0431\u04B1\u0440\u044B\u043D";return"\u0436\u0430\u0440\u0442\u044B \u043C\u0438\u043D\u0443\u0442"},lessThanXMinutes:{regular:{one:"1 \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u0430\u0437",singularNominative:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u0430\u0437",singularGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u0430\u0437",pluralGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u0430\u0437"},future:{one:"\u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u043A\u0435\u043C ",singularNominative:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u043A\u0435\u043C",singularGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u043A\u0435\u043C",pluralGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u043A\u0435\u043C"}},xMinutes:{regular:{singularNominative:"{{count}} \u043C\u0438\u043D\u0443\u0442",singularGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442",pluralGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442"},past:{singularNominative:"{{count}} \u043C\u0438\u043D\u0443\u0442 \u0431\u04B1\u0440\u044B\u043D",singularGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442 \u0431\u04B1\u0440\u044B\u043D",pluralGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442 \u0431\u04B1\u0440\u044B\u043D"},future:{singularNominative:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}},aboutXHours:{regular:{singularNominative:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0441\u0430\u0493\u0430\u0442",singularGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0441\u0430\u0493\u0430\u0442",pluralGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0441\u0430\u0493\u0430\u0442"},future:{singularNominative:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0441\u0430\u0493\u0430\u0442\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0441\u0430\u0493\u0430\u0442\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0441\u0430\u0493\u0430\u0442\u0442\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}},xHours:{regular:{singularNominative:"{{count}} \u0441\u0430\u0493\u0430\u0442",singularGenitive:"{{count}} \u0441\u0430\u0493\u0430\u0442",pluralGenitive:"{{count}} \u0441\u0430\u0493\u0430\u0442"}},xDays:{regular:{singularNominative:"{{count}} \u043A\u04AF\u043D",singularGenitive:"{{count}} \u043A\u04AF\u043D",pluralGenitive:"{{count}} \u043A\u04AF\u043D"},future:{singularNominative:"{{count}} \u043A\u04AF\u043D\u043D\u0435\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"{{count}} \u043A\u04AF\u043D\u043D\u0435\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"{{count}} \u043A\u04AF\u043D\u043D\u0435\u043D \u043A\u0435\u0439\u0456\u043D"}},aboutXWeeks:{type:"weeks",one:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D 1 \u0430\u043F\u0442\u0430",other:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0430\u043F\u0442\u0430"},xWeeks:{type:"weeks",one:"1 \u0430\u043F\u0442\u0430",other:"{{count}} \u0430\u043F\u0442\u0430"},aboutXMonths:{regular:{singularNominative:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0430\u0439",singularGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0430\u0439",pluralGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0430\u0439"},future:{singularNominative:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0430\u0439\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0430\u0439\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0430\u0439\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}},xMonths:{regular:{singularNominative:"{{count}} \u0430\u0439",singularGenitive:"{{count}} \u0430\u0439",pluralGenitive:"{{count}} \u0430\u0439"}},aboutXYears:{regular:{singularNominative:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0436\u044B\u043B",singularGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0436\u044B\u043B",pluralGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0436\u044B\u043B"},future:{singularNominative:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"\u0448\u0430\u043C\u0430\u043C\u0435\u043D {{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}},xYears:{regular:{singularNominative:"{{count}} \u0436\u044B\u043B",singularGenitive:"{{count}} \u0436\u044B\u043B",pluralGenitive:"{{count}} \u0436\u044B\u043B"},future:{singularNominative:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}},overXYears:{regular:{singularNominative:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u0430\u0441\u0442\u0430\u043C",singularGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u0430\u0441\u0442\u0430\u043C",pluralGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u0430\u0441\u0442\u0430\u043C"},future:{singularNominative:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u0430\u0441\u0442\u0430\u043C",singularGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u0430\u0441\u0442\u0430\u043C",pluralGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u0430\u0441\u0442\u0430\u043C"}},almostXYears:{regular:{singularNominative:"{{count}} \u0436\u044B\u043B\u0493\u0430 \u0436\u0430\u049B\u044B\u043D",singularGenitive:"{{count}} \u0436\u044B\u043B\u0493\u0430 \u0436\u0430\u049B\u044B\u043D",pluralGenitive:"{{count}} \u0436\u044B\u043B\u0493\u0430 \u0436\u0430\u049B\u044B\u043D"},future:{singularNominative:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",singularGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D",pluralGenitive:"{{count}} \u0436\u044B\u043B\u0434\u0430\u043D \u043A\u0435\u0439\u0456\u043D"}}},y=function G(J,X,Z){var B=m[J];if(typeof B==="function")return B(Z);if(B.type==="weeks")return X===1?B.one:B.other.replace("{{count}}",String(X));if(Z!==null&&Z!==void 0&&Z.addSuffix)if(Z.comparison&&Z.comparison>0)if(B.future)return N(B.future,X);else return N(B.regular,X)+" \u043A\u0435\u0439\u0456\u043D";else if(B.past)return N(B.past,X);else return N(B.regular,X)+" \u0431\u04B1\u0440\u044B\u043D";else return N(B.regular,X)};function M(G){return function(){var J=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X=J.width?String(J.width):G.defaultWidth,Z=G.formats[X]||G.formats[G.defaultWidth];return Z}}var c={full:"EEEE, do MMMM y '\u0436.'",long:"do MMMM y '\u0436.'",medium:"d MMM y '\u0436.'",short:"dd.MM.yyyy"},p={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},u={any:"{{date}}, {{time}}"},d={date:M({formats:c,defaultWidth:"full"}),time:M({formats:p,defaultWidth:"full"}),dateTime:M({formats:u,defaultWidth:"any"})},wG=7,l=365.2425,i=Math.pow(10,8)*24*60*60*1000,vG=-i,bG=604800000,hG=86400000,fG=60000,kG=3600000,_G=1000,gG=525600,mG=43200,yG=1440,cG=60,pG=3,uG=12,dG=4,s=3600,lG=60,$=s*24,iG=$*7,r=$*l,n=r/12,sG=n*3,j=Symbol.for("constructDateFrom");function L(G,J){if(typeof G==="function")return G(J);if(G&&K(G)==="object"&&j in G)return G[j](J);if(G instanceof Date)return new G.constructor(J);return new Date(J)}function o(G){for(var J=arguments.length,X=new Array(J>1?J-1:0),Z=1;Z<J;Z++)X[Z-1]=arguments[Z];var B=L.bind(null,G||X.find(function(C){return K(C)==="object"}));return X.map(B)}function a(){return V}function rG(G){V=G}var V={};function t(G,J){return L(J||G,G)}function O(G,J){var X,Z,B,C,U,H,Y=a(),Q=(X=(Z=(B=(C=J===null||J===void 0?void 0:J.weekStartsOn)!==null&&C!==void 0?C:J===null||J===void 0||(U=J.locale)===null||U===void 0||(U=U.options)===null||U===void 0?void 0:U.weekStartsOn)!==null&&B!==void 0?B:Y.weekStartsOn)!==null&&Z!==void 0?Z:(H=Y.locale)===null||H===void 0||(H=H.options)===null||H===void 0?void 0:H.weekStartsOn)!==null&&X!==void 0?X:0,q=t(G,J===null||J===void 0?void 0:J.in),E=q.getDay(),DG=(E<Q?7:0)+E-Q;return q.setDate(q.getDate()-DG),q.setHours(0,0,0,0),q}function P(G,J,X){var Z=o(X===null||X===void 0?void 0:X.in,G,J),B=b(Z,2),C=B[0],U=B[1];return+O(C,X)===+O(U,X)}function e(G){var J=R[G];return"'\u04E9\u0442\u043A\u0435\u043D "+J+" \u0441\u0430\u0493\u0430\u0442' p'-\u0434\u0435'"}function D(G){var J=R[G];return"'"+J+" \u0441\u0430\u0493\u0430\u0442' p'-\u0434\u0435'"}function GG(G){var J=R[G];return"'\u043A\u0435\u043B\u0435\u0441\u0456 "+J+" \u0441\u0430\u0493\u0430\u0442' p'-\u0434\u0435'"}var R=["\u0436\u0435\u043A\u0441\u0435\u043D\u0431\u0456\u0434\u0435","\u0434\u04AF\u0439\u0441\u0435\u043D\u0431\u0456\u0434\u0435","\u0441\u0435\u0439\u0441\u0435\u043D\u0431\u0456\u0434\u0435","\u0441\u04D9\u0440\u0441\u0435\u043D\u0431\u0456\u0434\u0435","\u0431\u0435\u0439\u0441\u0435\u043D\u0431\u0456\u0434\u0435","\u0436\u04B1\u043C\u0430\u0434\u0430","\u0441\u0435\u043D\u0431\u0456\u0434\u0435"],JG={lastWeek:function G(J,X,Z){var B=J.getDay();if(P(J,X,Z))return D(B);else return e(B)},yesterday:"'\u043A\u0435\u0448\u0435 \u0441\u0430\u0493\u0430\u0442' p'-\u0434\u0435'",today:"'\u0431\u04AF\u0433\u0456\u043D \u0441\u0430\u0493\u0430\u0442' p'-\u0434\u0435'",tomorrow:"'\u0435\u0440\u0442\u0435\u04A3 \u0441\u0430\u0493\u0430\u0442' p'-\u0434\u0435'",nextWeek:function G(J,X,Z){var B=J.getDay();if(P(J,X,Z))return D(B);else return GG(B)},other:"P"},XG=function G(J,X,Z,B){var C=JG[J];if(typeof C==="function")return C(X,Z,B);return C};function T(G){return function(J,X){var Z=X!==null&&X!==void 0&&X.context?String(X.context):"standalone",B;if(Z==="formatting"&&G.formattingValues){var C=G.defaultFormattingWidth||G.defaultWidth,U=X!==null&&X!==void 0&&X.width?String(X.width):C;B=G.formattingValues[U]||G.formattingValues[C]}else{var H=G.defaultWidth,Y=X!==null&&X!==void 0&&X.width?String(X.width):G.defaultWidth;B=G.values[Y]||G.values[H]}var Q=G.argumentCallback?G.argumentCallback(J):J;return B[Q]}}var ZG={narrow:["\u0431.\u0437.\u0434.","\u0431.\u0437."],abbreviated:["\u0431.\u0437.\u0434.","\u0431.\u0437."],wide:["\u0431\u0456\u0437\u0434\u0456\u04A3 \u0437\u0430\u043C\u0430\u043D\u044B\u043C\u044B\u0437\u0493\u0430 \u0434\u0435\u0439\u0456\u043D","\u0431\u0456\u0437\u0434\u0456\u04A3 \u0437\u0430\u043C\u0430\u043D\u044B\u043C\u044B\u0437"]},BG={narrow:["1","2","3","4"],abbreviated:["1-\u0448\u0456 \u0442\u043E\u049B.","2-\u0448\u0456 \u0442\u043E\u049B.","3-\u0448\u0456 \u0442\u043E\u049B.","4-\u0448\u0456 \u0442\u043E\u049B."],wide:["1-\u0448\u0456 \u0442\u043E\u049B\u0441\u0430\u043D","2-\u0448\u0456 \u0442\u043E\u049B\u0441\u0430\u043D","3-\u0448\u0456 \u0442\u043E\u049B\u0441\u0430\u043D","4-\u0448\u0456 \u0442\u043E\u049B\u0441\u0430\u043D"]},CG={narrow:["\u049A","\u0410","\u041D","\u0421","\u041C","\u041C","\u0428","\u0422","\u049A","\u049A","\u049A","\u0416"],abbreviated:["\u049B\u0430\u04A3","\u0430\u049B\u043F","\u043D\u0430\u0443","\u0441\u04D9\u0443","\u043C\u0430\u043C","\u043C\u0430\u0443","\u0448\u0456\u043B","\u0442\u0430\u043C","\u049B\u044B\u0440","\u049B\u0430\u0437","\u049B\u0430\u0440","\u0436\u0435\u043B"],wide:["\u049B\u0430\u04A3\u0442\u0430\u0440","\u0430\u049B\u043F\u0430\u043D","\u043D\u0430\u0443\u0440\u044B\u0437","\u0441\u04D9\u0443\u0456\u0440","\u043C\u0430\u043C\u044B\u0440","\u043C\u0430\u0443\u0441\u044B\u043C","\u0448\u0456\u043B\u0434\u0435","\u0442\u0430\u043C\u044B\u0437","\u049B\u044B\u0440\u043A\u04AF\u0439\u0435\u043A","\u049B\u0430\u0437\u0430\u043D","\u049B\u0430\u0440\u0430\u0448\u0430","\u0436\u0435\u043B\u0442\u043E\u049B\u0441\u0430\u043D"]},UG={narrow:["\u049A","\u0410","\u041D","\u0421","\u041C","\u041C","\u0428","\u0422","\u049A","\u049A","\u049A","\u0416"],abbreviated:["\u049B\u0430\u04A3","\u0430\u049B\u043F","\u043D\u0430\u0443","\u0441\u04D9\u0443","\u043C\u0430\u043C","\u043C\u0430\u0443","\u0448\u0456\u043B","\u0442\u0430\u043C","\u049B\u044B\u0440","\u049B\u0430\u0437","\u049B\u0430\u0440","\u0436\u0435\u043B"],wide:["\u049B\u0430\u04A3\u0442\u0430\u0440","\u0430\u049B\u043F\u0430\u043D","\u043D\u0430\u0443\u0440\u044B\u0437","\u0441\u04D9\u0443\u0456\u0440","\u043C\u0430\u043C\u044B\u0440","\u043C\u0430\u0443\u0441\u044B\u043C","\u0448\u0456\u043B\u0434\u0435","\u0442\u0430\u043C\u044B\u0437","\u049B\u044B\u0440\u043A\u04AF\u0439\u0435\u043A","\u049B\u0430\u0437\u0430\u043D","\u049B\u0430\u0440\u0430\u0448\u0430","\u0436\u0435\u043B\u0442\u043E\u049B\u0441\u0430\u043D"]},HG={narrow:["\u0416","\u0414","\u0421","\u0421","\u0411","\u0416","\u0421"],short:["\u0436\u0441","\u0434\u0441","\u0441\u0441","\u0441\u0440","\u0431\u0441","\u0436\u043C","\u0441\u0431"],abbreviated:["\u0436\u0441","\u0434\u0441","\u0441\u0441","\u0441\u0440","\u0431\u0441","\u0436\u043C","\u0441\u0431"],wide:["\u0436\u0435\u043A\u0441\u0435\u043D\u0431\u0456","\u0434\u04AF\u0439\u0441\u0435\u043D\u0431\u0456","\u0441\u0435\u0439\u0441\u0435\u043D\u0431\u0456","\u0441\u04D9\u0440\u0441\u0435\u043D\u0431\u0456","\u0431\u0435\u0439\u0441\u0435\u043D\u0431\u0456","\u0436\u04B1\u043C\u0430","\u0441\u0435\u043D\u0431\u0456"]},QG={narrow:{am:"\u0422\u0414",pm:"\u0422\u041A",midnight:"\u0442\u04AF\u043D \u043E\u0440\u0442\u0430\u0441\u044B",noon:"\u0442\u04AF\u0441",morning:"\u0442\u0430\u04A3",afternoon:"\u043A\u04AF\u043D\u0434\u0456\u0437",evening:"\u043A\u0435\u0448",night:"\u0442\u04AF\u043D"},wide:{am:"\u0422\u0414",pm:"\u0422\u041A",midnight:"\u0442\u04AF\u043D \u043E\u0440\u0442\u0430\u0441\u044B",noon:"\u0442\u04AF\u0441",morning:"\u0442\u0430\u04A3",afternoon:"\u043A\u04AF\u043D\u0434\u0456\u0437",evening:"\u043A\u0435\u0448",night:"\u0442\u04AF\u043D"}},YG={narrow:{am:"\u0422\u0414",pm:"\u0422\u041A",midnight:"\u0442\u04AF\u043D \u043E\u0440\u0442\u0430\u0441\u044B\u043D\u0434\u0430",noon:"\u0442\u04AF\u0441",morning:"\u0442\u0430\u04A3",afternoon:"\u043A\u04AF\u043D",evening:"\u043A\u0435\u0448",night:"\u0442\u04AF\u043D"},wide:{am:"\u0422\u0414",pm:"\u0422\u041A",midnight:"\u0442\u04AF\u043D \u043E\u0440\u0442\u0430\u0441\u044B\u043D\u0434\u0430",noon:"\u0442\u04AF\u0441\u0442\u0435",morning:"\u0442\u0430\u04A3\u0435\u0440\u0442\u0435\u04A3",afternoon:"\u043A\u04AF\u043D\u0434\u0456\u0437",evening:"\u043A\u0435\u0448\u0442\u0435",night:"\u0442\u04AF\u043D\u0434\u0435"}},x={0:"-\u0448\u0456",1:"-\u0448\u0456",2:"-\u0448\u0456",3:"-\u0448\u0456",4:"-\u0448\u0456",5:"-\u0448\u0456",6:"-\u0448\u044B",7:"-\u0448\u0456",8:"-\u0448\u0456",9:"-\u0448\u044B",10:"-\u0448\u044B",20:"-\u0448\u044B",30:"-\u0448\u044B",40:"-\u0448\u044B",50:"-\u0448\u0456",60:"-\u0448\u044B",70:"-\u0448\u0456",80:"-\u0448\u0456",90:"-\u0448\u044B",100:"-\u0448\u0456"},qG=function G(J,X){var Z=Number(J),B=Z%10,C=Z>=100?100:null,U=x[Z]||x[B]||C&&x[C]||"";return Z+U},KG={ordinalNumber:qG,era:T({values:ZG,defaultWidth:"wide"}),quarter:T({values:BG,defaultWidth:"wide",argumentCallback:function G(J){return J-1}}),month:T({values:CG,defaultWidth:"wide",formattingValues:UG,defaultFormattingWidth:"wide"}),day:T({values:HG,defaultWidth:"wide"}),dayPeriod:T({values:QG,defaultWidth:"any",formattingValues:YG,defaultFormattingWidth:"wide"})};function A(G){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=X.width,B=Z&&G.matchPatterns[Z]||G.matchPatterns[G.defaultMatchWidth],C=J.match(B);if(!C)return null;var U=C[0],H=Z&&G.parsePatterns[Z]||G.parsePatterns[G.defaultParseWidth],Y=Array.isArray(H)?NG(H,function(E){return E.test(U)}):EG(H,function(E){return E.test(U)}),Q;Q=G.valueCallback?G.valueCallback(Y):Y,Q=X.valueCallback?X.valueCallback(Q):Q;var q=J.slice(U.length);return{value:Q,rest:q}}}function EG(G,J){for(var X in G)if(Object.prototype.hasOwnProperty.call(G,X)&&J(G[X]))return X;return}function NG(G,J){for(var X=0;X<G.length;X++)if(J(G[X]))return X;return}function TG(G){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=J.match(G.matchPattern);if(!Z)return null;var B=Z[0],C=J.match(G.parsePattern);if(!C)return null;var U=G.valueCallback?G.valueCallback(C[0]):C[0];U=X.valueCallback?X.valueCallback(U):U;var H=J.slice(B.length);return{value:U,rest:H}}}var AG=/^(\d+)(-?(ші|шы))?/i,IG=/\d+/i,zG={narrow:/^((б )?з\.?\s?д\.?)/i,abbreviated:/^((б )?з\.?\s?д\.?)/i,wide:/^(біздің заманымызға дейін|біздің заманымыз|біздің заманымыздан)/i},MG={any:[/^б/i,/^з/i]},RG={narrow:/^[1234]/i,abbreviated:/^[1234](-?ші)? тоқ.?/i,wide:/^[1234](-?ші)? тоқсан/i},xG={any:[/1/i,/2/i,/3/i,/4/i]},SG={narrow:/^(қ|а|н|с|м|мау|ш|т|қыр|қаз|қар|ж)/i,abbreviated:/^(қаң|ақп|нау|сәу|мам|мау|шіл|там|қыр|қаз|қар|жел)/i,wide:/^(қаңтар|ақпан|наурыз|сәуір|мамыр|маусым|шілде|тамыз|қыркүйек|қазан|қараша|желтоқсан)/i},WG={narrow:[/^қ/i,/^а/i,/^н/i,/^с/i,/^м/i,/^м/i,/^ш/i,/^т/i,/^қ/i,/^қ/i,/^қ/i,/^ж/i],abbreviated:[/^қаң/i,/^ақп/i,/^нау/i,/^сәу/i,/^мам/i,/^мау/i,/^шіл/i,/^там/i,/^қыр/i,/^қаз/i,/^қар/i,/^жел/i],any:[/^қ/i,/^а/i,/^н/i,/^с/i,/^м/i,/^м/i,/^ш/i,/^т/i,/^қ/i,/^қ/i,/^қ/i,/^ж/i]},$G={narrow:/^(ж|д|с|с|б|ж|с)/i,short:/^(жс|дс|сс|ср|бс|жм|сб)/i,wide:/^(жексенбі|дүйсенбі|сейсенбі|сәрсенбі|бейсенбі|жұма|сенбі)/i},jG={narrow:[/^ж/i,/^д/i,/^с/i,/^с/i,/^б/i,/^ж/i,/^с/i],short:[/^жс/i,/^дс/i,/^сс/i,/^ср/i,/^бс/i,/^жм/i,/^сб/i],any:[/^ж[ек]/i,/^д[үй]/i,/^сe[й]/i,/^сә[р]/i,/^б[ей]/i,/^ж[ұм]/i,/^се[н]/i]},LG={narrow:/^Т\.?\s?[ДК]\.?|түн ортасында|((түсте|таңертең|таңда|таңертең|таңмен|таң|күндіз|күн|кеште|кеш|түнде|түн)\.?)/i,wide:/^Т\.?\s?[ДК]\.?|түн ортасында|((түсте|таңертең|таңда|таңертең|таңмен|таң|күндіз|күн|кеште|кеш|түнде|түн)\.?)/i,any:/^Т\.?\s?[ДК]\.?|түн ортасында|((түсте|таңертең|таңда|таңертең|таңмен|таң|күндіз|күн|кеште|кеш|түнде|түн)\.?)/i},VG={any:{am:/^ТД/i,pm:/^ТК/i,midnight:/^түн орта/i,noon:/^күндіз/i,morning:/таң/i,afternoon:/түс/i,evening:/кеш/i,night:/түн/i}},OG={ordinalNumber:TG({matchPattern:AG,parsePattern:IG,valueCallback:function G(J){return parseInt(J,10)}}),era:A({matchPatterns:zG,defaultMatchWidth:"wide",parsePatterns:MG,defaultParseWidth:"any"}),quarter:A({matchPatterns:RG,defaultMatchWidth:"wide",parsePatterns:xG,defaultParseWidth:"any",valueCallback:function G(J){return J+1}}),month:A({matchPatterns:SG,defaultMatchWidth:"wide",parsePatterns:WG,defaultParseWidth:"any"}),day:A({matchPatterns:$G,defaultMatchWidth:"wide",parsePatterns:jG,defaultParseWidth:"any"}),dayPeriod:A({matchPatterns:LG,defaultMatchWidth:"wide",parsePatterns:VG,defaultParseWidth:"any"})},PG={code:"kk",formatDistance:y,formatLong:d,formatRelative:XG,localize:KG,match:OG,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=I(I({},window.dateFns),{},{locale:I(I({},(z=window.dateFns)===null||z===void 0?void 0:z.locale),{},{kk:PG})})})();

//# debugId=CBB89493BFC262CF64756E2164756E21

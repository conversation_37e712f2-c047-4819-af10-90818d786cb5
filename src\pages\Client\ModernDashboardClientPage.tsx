import React, { useState } from 'react';
import { ThemeProvider } from '../../contexts/ThemeContext';
import ModernLayout from '../../components/layout/ModernLayout';
import AdaptableCard from '../../components/ui/AdaptableCard';
import {
  FaHome,
  FaUser,
  FaBell,
  FaBuilding,
  FaFileAlt,
  FaCalculator,
  FaChartBar,
  FaPlus,
  FaEye,
  FaDownload,
} from 'react-icons/fa';

const ModernDashboardClientPage = () => {
  const [showJuridicalForm, setShowJuridicalForm] = useState(false);

  // Configuration de la sidebar pour le client
  const sidebarItems = [
    {
      key: 'dashboard',
      path: '/client/dashboard',
      title: 'Tableau de bord',
      icon: <FaHome />,
    },
    {
      key: 'profile',
      path: '/client/profile',
      title: 'Profil',
      icon: <FaUser />,
    },
    {
      key: 'notifications',
      path: '/client/notifications',
      title: 'Notifications',
      icon: <FaBell />,
      badge: '3',
    },
    {
      key: 'domiciliation',
      path: '/client/domiciliation',
      title: 'Domiciliation',
      icon: <FaBuilding />,
    },
    {
      key: 'juridique',
      path: '/client/juridique',
      title: 'Services Juridiques',
      icon: <FaFileAlt />,
    },
    {
      key: 'comptabilite',
      path: '/client/comptabilite',
      title: 'Comptabilité',
      icon: <FaCalculator />,
    },
  ];

  // Données de démonstration
  const stats = [
    {
      title: 'Services Actifs',
      value: '12',
      change: '+2.5%',
      changeType: 'positive',
      icon: <FaChartBar className="text-blue-500" />,
    },
    {
      title: 'Domiciliations',
      value: '3',
      change: '+1',
      changeType: 'positive',
      icon: <FaBuilding className="text-green-500" />,
    },
    {
      title: 'Documents',
      value: '24',
      change: '+5',
      changeType: 'positive',
      icon: <FaFileAlt className="text-purple-500" />,
    },
    {
      title: 'En Attente',
      value: '2',
      change: '-1',
      changeType: 'negative',
      icon: <FaBell className="text-orange-500" />,
    },
  ];

  const recentServices = [
    {
      id: 1,
      type: 'Domiciliation',
      company: 'SARL TECH SOLUTIONS',
      status: 'Actif',
      date: '2024-01-15',
      statusColor: 'green',
    },
    {
      id: 2,
      type: 'Service Juridique',
      company: 'Création de société',
      status: 'En cours',
      date: '2024-01-10',
      statusColor: 'yellow',
    },
    {
      id: 3,
      type: 'Comptabilité',
      company: 'Déclaration fiscale',
      status: 'Terminé',
      date: '2024-01-05',
      statusColor: 'blue',
    },
  ];

  const quickActions = [
    {
      title: 'Nouvelle Domiciliation',
      description: 'Créer une nouvelle demande de domiciliation',
      icon: <FaBuilding className="text-blue-500" />,
      action: () => console.log('Nouvelle domiciliation'),
    },
    {
      title: 'Service Juridique',
      description: 'Demander un service juridique',
      icon: <FaFileAlt className="text-green-500" />,
      action: () => setShowJuridicalForm(true),
    },
    {
      title: 'Comptabilité',
      description: 'Services comptables',
      icon: <FaCalculator className="text-purple-500" />,
      action: () => console.log('Comptabilité'),
    },
  ];

  return (
    <ThemeProvider>
      <ModernLayout sidebarItems={sidebarItems} userType="client">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Tableau de bord
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Bienvenue sur votre espace client
              </p>
            </div>
            <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors duration-200">
              <FaPlus />
              <span>Nouveau service</span>
            </button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <AdaptableCard key={index} className="hover:shadow-lg transition-shadow duration-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                      {stat.value}
                    </p>
                    <p className={`text-sm mt-1 ${
                      stat.changeType === 'positive' 
                        ? 'text-green-600 dark:text-green-400' 
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {stat.change}
                    </p>
                  </div>
                  <div className="text-2xl">
                    {stat.icon}
                  </div>
                </div>
              </AdaptableCard>
            ))}
          </div>

          {/* Quick Actions */}
          <AdaptableCard>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Actions rapides
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {quickActions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.action}
                  className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200 text-left"
                >
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">
                      {action.icon}
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {action.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {action.description}
                      </p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </AdaptableCard>

          {/* Recent Services */}
          <AdaptableCard>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Services récents
              </h2>
              <button className="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                Voir tout
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Type</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Service</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Statut</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Date</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {recentServices.map((service) => (
                    <tr key={service.id} className="border-b border-gray-100 dark:border-gray-800">
                      <td className="py-3 px-4 text-gray-900 dark:text-white">{service.type}</td>
                      <td className="py-3 px-4 text-gray-900 dark:text-white">{service.company}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          service.statusColor === 'green' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : service.statusColor === 'yellow'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                            : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                        }`}>
                          {service.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-gray-600 dark:text-gray-400">{service.date}</td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <button className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200">
                            <FaEye />
                          </button>
                          <button className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200">
                            <FaDownload />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </AdaptableCard>
        </div>
      </ModernLayout>
    </ThemeProvider>
  );
};

export default ModernDashboardClientPage;

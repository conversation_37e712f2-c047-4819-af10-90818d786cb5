import type { ContextOptions, DateArg } from "./types.js";
/**
 * The {@link getMinutes} function options.
 */
export interface GetMinutesOptions extends ContextOptions<Date> {}
/**
 * @name getMinutes
 * @category Minute Helpers
 * @summary Get the minutes of the given date.
 *
 * @description
 * Get the minutes of the given date.
 *
 * @param date - The given date
 * @param options - The options
 *
 * @returns The minutes
 *
 * @example
 * // Get the minutes of 29 February 2012 11:45:05:
 * const result = getMinutes(new Date(2012, 1, 29, 11, 45, 5))
 * //=> 45
 */
export declare function getMinutes(
  date: DateArg<Date> & {},
  options?: GetMinutesOptions | undefined,
): number;

import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTheme } from '../../contexts/ThemeContext';
import { SIDE_NAV_WIDTH, SIDE_NAV_COLLAPSED_WIDTH } from '../../configs/theme.config';
import {
  FaHome,
  FaUser,
  FaBell,
  FaBuilding,
  FaFileAlt,
  FaCalculator,
  FaChevronLeft,
  FaChevronRight,
} from 'react-icons/fa';

interface SidebarItem {
  key: string;
  path: string;
  title: string;
  icon: React.ReactNode;
  badge?: string;
  subMenu?: SidebarItem[];
}

interface ModernSidebarProps {
  sidebarItems: SidebarItem[];
  userType: 'client' | 'enterprise' | 'admin';
}

const ModernSidebar: React.FC<ModernSidebarProps> = ({ sidebarItems, userType }) => {
  const { theme, toggleSidebar } = useTheme();
  const location = useLocation();
  const { sideNavCollapse } = theme.layout;

  const sidebarWidth = sideNavCollapse ? SIDE_NAV_COLLAPSED_WIDTH : SIDE_NAV_WIDTH;

  const sidebarClass = [
    'fixed left-0 top-0 h-full z-40 transition-all duration-300 ease-in-out',
    'bg-white dark:bg-gray-900',
    'border-r border-gray-200 dark:border-gray-700',
    'shadow-lg',
    sideNavCollapse ? 'w-20' : 'w-70'
  ].join(' ');

  const isActiveLink = (path: string) => {
    return location.pathname === path;
  };

  const renderMenuItem = (item: SidebarItem, level = 0) => {
    const isActive = isActiveLink(item.path);

    const menuItemClass = [
      'flex items-center px-4 py-3 mx-2 rounded-lg transition-all duration-200',
      'hover:bg-gray-100 dark:hover:bg-gray-800',
      'group relative',
      isActive ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border-r-2 border-blue-500' : 'text-gray-700 dark:text-gray-300',
      sideNavCollapse ? 'justify-center' : '',
      level === 0 ? 'mb-1' : '',
      level > 0 && !sideNavCollapse ? 'ml-4' : ''
    ].filter(Boolean).join(' ');

    const iconClass = [
      'text-lg transition-colors duration-200',
      isActive ? 'text-blue-500' : 'text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200'
    ].join(' ');

    return (
      <Link key={item.key} to={item.path} className={menuItemClass}>
        <span className={iconClass}>
          {item.icon}
        </span>
        
        {!sideNavCollapse && (
          <>
            <span className="ml-3 font-medium text-sm">{item.title}</span>
            {item.badge && (
              <span className="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                {item.badge}
              </span>
            )}
          </>
        )}

        {/* Tooltip pour sidebar collapsed */}
        {sideNavCollapse && (
          <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-50">
            {item.title}
          </div>
        )}
      </Link>
    );
  };

  return (
    <div className={sidebarClass} style={{ width: sidebarWidth }}>
      {/* Header avec logo */}
      <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700">
        {!sideNavCollapse && (
          <div className="flex items-center">
            <img 
              src="/src/assets/logo/logo.png" 
              alt="Logo" 
              className="h-8 w-auto"
            />
            <span className="ml-2 text-xl font-bold text-gray-800 dark:text-white">
              MACHROUHI
            </span>
          </div>
        )}
        
        <button
          onClick={toggleSidebar}
          className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
        >
          {sideNavCollapse ? (
            <FaChevronRight className="text-gray-500 dark:text-gray-400" />
          ) : (
            <FaChevronLeft className="text-gray-500 dark:text-gray-400" />
          )}
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 py-4 overflow-y-auto">
        <div className="space-y-1">
          {sidebarItems.map((item) => renderMenuItem(item))}
        </div>
      </nav>

      {/* Footer avec informations utilisateur */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4">
        {!sideNavCollapse ? (
          <div className="flex items-center">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <FaUser className="text-white text-sm" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {userType === 'client' ? 'Client' : userType === 'enterprise' ? 'Entreprise' : 'Admin'}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Dashboard
              </p>
            </div>
          </div>
        ) : (
          <div className="flex justify-center">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <FaUser className="text-white text-sm" />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ModernSidebar;

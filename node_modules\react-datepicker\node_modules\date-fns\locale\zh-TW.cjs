"use strict";
exports.zhTW = void 0;
var _index = require("./zh-TW/_lib/formatDistance.cjs");
var _index2 = require("./zh-TW/_lib/formatLong.cjs");
var _index3 = require("./zh-TW/_lib/formatRelative.cjs");
var _index4 = require("./zh-TW/_lib/localize.cjs");
var _index5 = require("./zh-TW/_lib/match.cjs");

/**
 * @category Locales
 * @summary Chinese Traditional locale.
 * @language Chinese Traditional
 * @iso-639-2 zho
 * <AUTHOR> [@tpai](https://github.com/tpai)
 * <AUTHOR> [@jackhsu978](https://github.com/jackhsu978)
 * <AUTHOR> [@skyuplam](https://github.com/skyuplam)
 */
const zhTW = (exports.zhTW = {
  code: "zh-TW",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});

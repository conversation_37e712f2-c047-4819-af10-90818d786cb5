import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { HEADER_HEIGHT, SIDE_NAV_WIDTH, SIDE_NAV_COLLAPSED_WIDTH } from '../../configs/theme.config';
import ModernSidebar from './ModernSidebar';
import ModernHeader from './ModernHeader';
import useResponsive from '../../hooks/useResponsive';

interface SidebarItem {
  key: string;
  path: string;
  title: string;
  icon: React.ReactNode;
  badge?: string;
  subMenu?: SidebarItem[];
}

interface ModernLayoutProps {
  children: React.ReactNode;
  sidebarItems: SidebarItem[];
  userType: 'client' | 'enterprise' | 'admin';
  className?: string;
}

const ModernLayout: React.FC<ModernLayoutProps> = ({
  children,
  sidebarItems,
  userType,
  className
}) => {
  const { theme } = useTheme();
  const { isLg } = useResponsive();
  const { sideNavCollapse } = theme.layout;

  const contentMarginLeft = isLg 
    ? (sideNavCollapse ? SIDE_NAV_COLLAPSED_WIDTH : SIDE_NAV_WIDTH)
    : 0;

  const layoutClass = [
    'min-h-screen transition-all duration-300 ease-in-out',
    'bg-gray-50 dark:bg-gray-900',
    className || ''
  ].filter(Boolean).join(' ');

  const contentClass = [
    'transition-all duration-300 ease-in-out',
    'min-h-screen',
    sideNavCollapse && isLg ? 'lg:ml-20' : '',
    !sideNavCollapse && isLg ? 'lg:ml-70' : ''
  ].filter(Boolean).join(' ');

  const mainContentClass = [
    'p-6',
    'transition-all duration-300 ease-in-out'
  ].join(' ');

  return (
    <div className={layoutClass}>
      {/* Sidebar */}
      <ModernSidebar 
        sidebarItems={sidebarItems}
        userType={userType}
      />

      {/* Mobile overlay */}
      {!isLg && !sideNavCollapse && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={() => {/* toggle sidebar */}}
        />
      )}

      {/* Main content area */}
      <div className={contentClass}>
        {/* Header */}
        <ModernHeader />

        {/* Page content */}
        <main 
          className={mainContentClass}
          style={{ 
            marginTop: HEADER_HEIGHT,
            minHeight: `calc(100vh - ${HEADER_HEIGHT}px)`
          }}
        >
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default ModernLayout;

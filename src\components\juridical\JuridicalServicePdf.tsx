import React from 'react';
import { Document, Page, Text, View, StyleSheet, Image } from '@react-pdf/renderer';
import logo from '../../assets/logo/logo.png'; 

const styles = StyleSheet.create({
  page: { padding: 40, fontSize: 12, lineHeight: 1.6 },
  section: { marginBottom: 15 },
  title: { fontSize: 16, textAlign: 'center', marginBottom: 25, fontWeight: 'bold' },
  subtitle: { fontSize: 14, marginBottom: 15, fontWeight: 'bold', color: '#2563eb' },
  bold: { fontWeight: 'bold' },
  logo: {
    width: 100,
    height: 'auto',
    marginBottom: 20,
    alignSelf: 'center',
  },
  table: {
    display: 'table',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    marginBottom: 15
  },
  tableRow: {
    margin: 'auto',
    flexDirection: 'row'
  },
  tableCol: {
    width: '50%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderLeftWidth: 0,
    borderTopWidth: 0
  },
  tableCell: {
    margin: 'auto',
    marginTop: 5,
    marginBottom: 5,
    fontSize: 10,
    padding: 5
  },
  associeTable: {
    display: 'table',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    marginBottom: 15
  },
  associeRow: {
    margin: 'auto',
    flexDirection: 'row'
  },
  associeCol: {
    width: '20%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderLeftWidth: 0,
    borderTopWidth: 0
  },
  associeCell: {
    margin: 'auto',
    marginTop: 3,
    marginBottom: 3,
    fontSize: 9,
    padding: 3
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 40,
    right: 40,
    textAlign: 'center',
    fontSize: 10,
    color: '#666'
  }
});

interface JuridicalServicePdfProps {
  formData: {
    selectedService: string;
    nomComplet: string;
    cin: string;
    adresse: string;
    villeCreation: string;
    typeSociete: string;
    objetsSociete: string;
    nom1: string;
    nom2: string;
    nom3: string;
    capitale: string;
    signature: string;
    associes: Array<{
      nom: string;
      cin: string;
      adresse: string;
      type: string;
      partSociale: string;
    }>;
    contratType: string;
    siegeSocial: string;
  };
}

const JuridicalServicePdf: React.FC<JuridicalServicePdfProps> = ({ formData }) => {
  const currentDate = new Date().toLocaleDateString('fr-FR');
  
  const getServiceTitle = (serviceId: string) => {
    const services = {
      'creation-societe': 'Création de société',
      'modification-statuts': 'Modification des statuts',
      'transfert-siege': 'Transfert de siège social',
      'changement-dirigeant': 'Changement de dirigeant',
      'changement-denomination': 'Changement de dénomination sociale',
      'modification-objet': 'Modification de l\'objet social',
      'augmentation-capital': 'Augmentation de capital',
      'reduction-capital': 'Réduction de capital',
      'cession-parts': 'Cession de parts sociales ou d\'actions',
      'entree-sortie-associe': 'Entrée ou sortie d\'un associé'
    };
    return services[serviceId] || 'Service Juridique';
  };

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* LOGO */}
        <Image style={styles.logo} src={logo} />

        <Text style={styles.title}>
          Dossier de {getServiceTitle(formData.selectedService)}
        </Text>

        {/* Informations du demandeur */}
        <Text style={styles.subtitle}>Informations du demandeur</Text>
        <View style={styles.table}>
          <View style={styles.tableRow}>
            <View style={styles.tableCol}>
              <Text style={styles.tableCell}>
                <Text style={styles.bold}>Nom complet:</Text> {formData.nomComplet}
              </Text>
            </View>
            <View style={styles.tableCol}>
              <Text style={styles.tableCell}>
                <Text style={styles.bold}>CIN:</Text> {formData.cin}
              </Text>
            </View>
          </View>
          <View style={styles.tableRow}>
            <View style={styles.tableCol}>
              <Text style={styles.tableCell}>
                <Text style={styles.bold}>Adresse:</Text> {formData.adresse}
              </Text>
            </View>
            <View style={styles.tableCol}>
              <Text style={styles.tableCell}>
                <Text style={styles.bold}>Ville:</Text> {formData.villeCreation}
              </Text>
            </View>
          </View>
        </View>

        {/* Informations de l'entreprise */}
        <Text style={styles.subtitle}>Informations de l'entreprise</Text>
        <View style={styles.table}>
          <View style={styles.tableRow}>
            <View style={styles.tableCol}>
              <Text style={styles.tableCell}>
                <Text style={styles.bold}>Forme juridique:</Text> {formData.typeSociete}
              </Text>
            </View>
            <View style={styles.tableCol}>
              <Text style={styles.tableCell}>
                <Text style={styles.bold}>Objet social:</Text> {formData.objetsSociete}
              </Text>
            </View>
          </View>
          <View style={styles.tableRow}>
            <View style={styles.tableCol}>
              <Text style={styles.tableCell}>
                <Text style={styles.bold}>Capital:</Text> {formData.capitale}
              </Text>
            </View>
            <View style={styles.tableCol}>
              <Text style={styles.tableCell}>
                <Text style={styles.bold}>Signature:</Text> {formData.signature}
              </Text>
            </View>
          </View>
        </View>

        {/* Noms proposés */}
        {(formData.nom1 || formData.nom2 || formData.nom3) && (
          <>
            <Text style={styles.subtitle}>Noms proposés pour l'entreprise</Text>
            <View style={styles.section}>
              {formData.nom1 && <Text>1. {formData.nom1}</Text>}
              {formData.nom2 && <Text>2. {formData.nom2}</Text>}
              {formData.nom3 && <Text>3. {formData.nom3}</Text>}
            </View>
          </>
        )}

        {/* Associés */}
        {formData.associes && formData.associes.length > 0 && (
          <>
            <Text style={styles.subtitle}>Liste des associés</Text>
            <View style={styles.associeTable}>
              <View style={styles.associeRow}>
                <View style={styles.associeCol}>
                  <Text style={[styles.associeCell, styles.bold]}>Nom</Text>
                </View>
                <View style={styles.associeCol}>
                  <Text style={[styles.associeCell, styles.bold]}>CIN</Text>
                </View>
                <View style={styles.associeCol}>
                  <Text style={[styles.associeCell, styles.bold]}>Adresse</Text>
                </View>
                <View style={styles.associeCol}>
                  <Text style={[styles.associeCell, styles.bold]}>Type</Text>
                </View>
                <View style={styles.associeCol}>
                  <Text style={[styles.associeCell, styles.bold]}>Part sociale</Text>
                </View>
              </View>
              {formData.associes.map((associe, index) => (
                <View key={index} style={styles.associeRow}>
                  <View style={styles.associeCol}>
                    <Text style={styles.associeCell}>{associe.nom}</Text>
                  </View>
                  <View style={styles.associeCol}>
                    <Text style={styles.associeCell}>{associe.cin}</Text>
                  </View>
                  <View style={styles.associeCol}>
                    <Text style={styles.associeCell}>{associe.adresse}</Text>
                  </View>
                  <View style={styles.associeCol}>
                    <Text style={styles.associeCell}>{associe.type}</Text>
                  </View>
                  <View style={styles.associeCol}>
                    <Text style={styles.associeCell}>{associe.partSociale}</Text>
                  </View>
                </View>
              ))}
            </View>
          </>
        )}

        {/* Informations contractuelles */}
        {(formData.contratType || formData.siegeSocial) && (
          <>
            <Text style={styles.subtitle}>Informations contractuelles</Text>
            <View style={styles.section}>
              {formData.contratType && (
                <Text>
                  <Text style={styles.bold}>Type de contrat:</Text> {formData.contratType}
                </Text>
              )}
              {formData.siegeSocial && (
                <Text style={{ marginTop: 5 }}>
                  <Text style={styles.bold}>Siège social:</Text> {formData.siegeSocial}
                </Text>
              )}
            </View>
          </>
        )}

        {/* Footer */}
        <Text style={styles.footer}>
          Document généré le {currentDate} - Service Juridique MACHROUHI AFFAIRES
        </Text>
      </Page>
    </Document>
  );
};

export default JuridicalServicePdf;

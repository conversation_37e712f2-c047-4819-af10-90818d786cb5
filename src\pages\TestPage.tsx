import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { setUserRole } from '../services/api';
import { useAuthContext } from '../contexts/AuthContext';

const TestPage = () => {
  const navigate = useNavigate();
  const { user, fetchUser } = useAuthContext();
  const [selectedRole, setSelectedRole] = useState<'client' | 'coursier' | 'entreprise'>(
    (user?.role as any) || 'client'
  );

  // Mettre à jour le rôle de l'utilisateur
  const handleRoleChange = async (role: 'client' | 'coursier' | 'entreprise') => {
    setUserRole(role);
    setSelectedRole(role);
    await fetchUser();
  };

  // Naviguer vers la page de profil correspondante
  const goToProfile = () => {
    const profilePath = `/${selectedRole}/profile`;
    navigate(profilePath);
  };

  // Naviguer vers le dashboard moderne correspondant
  const goToDashboard = () => {
    const dashboardPath = `/${selectedRole}/modern-dashboard`;
    navigate(dashboardPath);
  };

  // Naviguer vers l'ancien dashboard (pour comparaison)
  const goToOldDashboard = () => {
    const dashboardPath = `/${selectedRole}/dashboard`;
    navigate(dashboardPath);
  };

  // Effacer les données du localStorage
  const clearLocalStorage = () => {
    localStorage.removeItem('mock_clientProfile');
    localStorage.removeItem('mock_coursierProfile');
    localStorage.removeItem('mock_entrepriseProfile');
    localStorage.removeItem('mock_userProfile');
    localStorage.removeItem('client_profile_image');
    localStorage.removeItem('coursier_profile_image');
    localStorage.removeItem('entreprise_profile_image');
    window.location.reload();
  };

  // Forcer la mise à jour de l'image de profil dans la NavBar
  const forceUpdateProfileImage = () => {
    if (user?.role) {
      const storageKey = `${user.role}_profile_image`;
      const storedImage = localStorage.getItem(storageKey);

      if (storedImage) {
        // Créer un événement de stockage personnalisé
        try {
          const storageEvent = new StorageEvent('storage', {
            key: storageKey,
            newValue: storedImage,
            oldValue: storedImage,
            storageArea: localStorage
          });

          // Déclencher l'événement
          window.dispatchEvent(storageEvent);

          alert('Événement de mise à jour de l\'image de profil déclenché');
        } catch (error) {
          console.error('Erreur lors de la création de l\'événement de stockage:', error);
          alert('Erreur lors de la mise à jour de l\'image de profil');
        }
      } else {
        alert('Aucune image de profil trouvée dans le localStorage');
      }
    } else {
      alert('Aucun rôle d\'utilisateur défini');
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Page de Test</h1>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Utilisateur actuel</h2>
        <div className="flex items-center mb-4">
          <div className="w-16 h-16 rounded-full overflow-hidden mr-4">
            <img
              src={user?.profileImage || 'https://via.placeholder.com/64'}
              alt="Profile"
              className="w-full h-full object-cover"
            />
          </div>
          <div>
            <p className="font-semibold">{user?.name || 'Utilisateur'}</p>
            <p className="text-gray-600">{user?.email || '<EMAIL>'}</p>
            <p className="text-gray-600">Rôle: {user?.role || 'Non défini'}</p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Changer de rôle</h2>
        <div className="flex flex-wrap gap-4 mb-4">
          <button
            className={`px-4 py-2 rounded-md ${
              selectedRole === 'client' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'
            }`}
            onClick={() => handleRoleChange('client')}
          >
            Client
          </button>
          <button
            className={`px-4 py-2 rounded-md ${
              selectedRole === 'coursier' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'
            }`}
            onClick={() => handleRoleChange('coursier')}
          >
            Coursier
          </button>
          <button
            className={`px-4 py-2 rounded-md ${
              selectedRole === 'entreprise' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'
            }`}
            onClick={() => handleRoleChange('entreprise')}
          >
            Entreprise
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Navigation</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="text-lg font-medium mb-2 text-blue-600">Dashboards Modernes</h3>
            <div className="flex flex-col gap-2">
              <button
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                onClick={goToDashboard}
              >
                🚀 Dashboard Moderne {selectedRole}
              </button>
              <button
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                onClick={goToProfile}
              >
                👤 Profil {selectedRole}
              </button>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-medium mb-2 text-gray-600">Dashboards Anciens</h3>
            <div className="flex flex-col gap-2">
              <button
                className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
                onClick={goToOldDashboard}
              >
                📊 Ancien Dashboard {selectedRole}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Mise à jour de l'image de profil</h2>
        <button
          className="px-4 py-2 bg-yellow-600 text-white rounded-md"
          onClick={forceUpdateProfileImage}
        >
          Forcer la mise à jour de l'image de profil
        </button>
        <p className="text-sm text-gray-600 mt-2">
          Cette action force la mise à jour de l'image de profil dans la NavBar.
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Réinitialisation</h2>
        <button
          className="px-4 py-2 bg-red-600 text-white rounded-md"
          onClick={clearLocalStorage}
        >
          Réinitialiser les données
        </button>
        <p className="text-sm text-gray-600 mt-2">
          Cette action effacera toutes les données stockées localement et rechargera la page.
        </p>
      </div>
    </div>
  );
};

export default TestPage;

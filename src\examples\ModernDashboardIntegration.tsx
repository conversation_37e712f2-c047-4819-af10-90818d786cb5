import React from 'react';
import { Routes, Route } from 'react-router-dom';

// Import des dashboards modernes
import ModernDashboardRouter from '../components/routing/ModernDashboardRouter';
import ModernDashboardClientPage from '../pages/Client/ModernDashboardClientPage';
import ModernDashboardEnterprisePage from '../pages/Enterprise/ModernDashboardEnterprisePage';
import ModernDashboardCourierPage from '../pages/Courier/ModernDashboardCourierPage';

/**
 * Exemple d'intégration des dashboards modernes dans votre routing
 * 
 * Option 1 : Routeur automatique (Recommandé)
 * - Détecte automatiquement le type d'utilisateur
 * - Affiche le dashboard approprié
 * 
 * Option 2 : Routes individuelles
 * - Contrôle manuel du routing
 * - Utile pour des cas spécifiques
 */

const ModernDashboardIntegration: React.FC = () => {
  return (
    <Routes>
      {/* Option 1 : Routeur automatique - Recommandé */}
      <Route path="/dashboard" element={<ModernDashboardRouter />} />
      
      {/* Option 2 : Routes individuelles */}
      <Route path="/client/dashboard" element={<ModernDashboardClientPage />} />
      <Route path="/enterprise/dashboard" element={<ModernDashboardEnterprisePage />} />
      <Route path="/courier/dashboard" element={<ModernDashboardCourierPage />} />
      
      {/* Routes de test pour développement */}
      <Route path="/test/client-dashboard" element={<ModernDashboardClientPage />} />
      <Route path="/test/enterprise-dashboard" element={<ModernDashboardEnterprisePage />} />
      <Route path="/test/courier-dashboard" element={<ModernDashboardCourierPage />} />
    </Routes>
  );
};

export default ModernDashboardIntegration;

/**
 * Instructions d'utilisation :
 * 
 * 1. Remplacer vos routes existantes :
 * 
 * // Ancien
 * <Route path="/client/dashboard" element={<DashboardClientPage />} />
 * 
 * // Nouveau
 * <Route path="/client/dashboard" element={<ModernDashboardClientPage />} />
 * 
 * 2. Ou utiliser le routeur automatique :
 * 
 * <Route path="/dashboard" element={<ModernDashboardRouter />} />
 * 
 * 3. Pour tester individuellement :
 * 
 * - http://localhost:3000/test/client-dashboard
 * - http://localhost:3000/test/enterprise-dashboard  
 * - http://localhost:3000/test/courier-dashboard
 * 
 * 4. Configuration du type d'utilisateur :
 * 
 * Le routeur automatique utilise le contexte AuthContext pour déterminer
 * le type d'utilisateur. Assurez-vous que votre user object contient :
 * 
 * - user.role = 'client' | 'enterprise' | 'courier'
 * - ou user.type = 'client' | 'enterprise' | 'courier'
 */

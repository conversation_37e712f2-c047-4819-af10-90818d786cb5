import { createBrowserRouter } from "react-router-dom";
import DashboardCoursierPage from "./pages/Coursier/DashboardCoursierPage";
import CommandePage from "./pages/Coursier/CommandePage";
import SignUp from "./pages/LandingPage/SignUp";
import SignIn from "./pages/LandingPage/SignIn";
import VerticalSignUp from "./pages/LandingPage/VerticalSignUp";
import { HomePageLandingPage } from "./pages/LandingPage/HomePageLandingPage";
import DashboardClientPage from "./pages/Client/DashboardClientPage";
import { HomePage } from "./pages/HomePage";
import { Layout } from "./pages/Layout";
import { DashboardEntreprisePage } from "./pages/Entreprise/DashboardEntreprisePage";
import { LandingPageLayout } from "./pages/LandingPage/LandingPageLayout";
import DashboardHomePage from "./pages/Coursier/DashboardHomePage";
import TestPage from "./pages/TestPage";
import AdminRoute from "./components/AdminRoute";

// Enterprise Pages are now directly imported in DashboardEntreprisePage

// Client Pages are now directly imported in DashboardClientPage

// Coursier Pages are now directly imported in DashboardCoursierPage

// Entreprise Pages are now directly imported in DashboardEntreprisePage

// Admin Pages are now directly imported in DashboardAdminPage
import DashboardAdminPage from "./pages/Admin/DashboardAdminPage";

// Import des nouveaux dashboards modernes
import ModernDashboardClientPage from "./pages/Client/ModernDashboardClientPage";
import ModernDashboardEnterprisePage from "./pages/Enterprise/ModernDashboardEnterprisePage";
import ModernDashboardCourierPage from "./pages/Courier/ModernDashboardCourierPage";
import ModernDashboardRouter from "./components/routing/ModernDashboardRouter";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import TestDashboardEnterprisePage from "./pages/Enterprise/TestDashboardEnterprisePage";
import MinimalTestPage from "./pages/Enterprise/MinimalTestPage";
import EmergencyTestPage from "./pages/Enterprise/EmergencyTestPage";
import WorkingDashboardEnterprise from "./pages/Enterprise/WorkingDashboardEnterprise";


const router = createBrowserRouter([
  {
    path: "/test",
    element: <TestPage />
  },
  {
    path: "/",
    element: <Layout />,

    children: [

      {
        path:"/", element: <LandingPageLayout />,
        children: [

          {path: "/", element: <HomePageLandingPage />},
          {path: "signin", element: <SignIn />},
          {path: "signup", element: <SignUp />},
          {path: "vertical-signup", element: <VerticalSignUp />},
        ],
      },

      {
        path: "coursier",
        element: <DashboardCoursierPage />,
        children: [
          { path: "dashboard", element: <DashboardHomePage /> },
        ],
      },

      // Note: Coursier pages are now directly integrated in DashboardCoursierPage
      // We keep only the detail page that needs a parameter
      { path: "coursier/commandes/:id", element: <CommandePage /> },

      // Client routes
      {
        path: "client",
        element: <DashboardClientPage />,
        children: [
          {path: "dashboard", element: <HomePage />},
        ]
      },

      // Note: Client pages are now directly integrated in DashboardClientPage

      // Client entreprise
      {
        path: "entreprise",
        element: <DashboardEntreprisePage />,
        children: [
          {path: "dashboard", element: <HomePage />},
        ]
      },

      // Note: Enterprise pages are now directly integrated in DashboardEntreprisePage

      // Admin routes
      {
        path: "admin",
        element: <AdminRoute><DashboardAdminPage /></AdminRoute>,
        children: [
          {path: "dashboard", element: <HomePage />},
        ]
      },

      // Note: Admin pages are now directly integrated in DashboardAdminPage

      // ========== NOUVEAUX DASHBOARDS MODERNES ==========

      // Route automatique qui détecte le type d'utilisateur (protégée)
      {
        path: "modern-dashboard",
        element: (
          <ProtectedRoute>
            <ModernDashboardRouter />
          </ProtectedRoute>
        )
      },

      // Routes individuelles pour les dashboards modernes (protégées)
      {
        path: "client/modern-dashboard",
        element: (
          <ProtectedRoute requiredRole="client">
            <ModernDashboardClientPage />
          </ProtectedRoute>
        )
      },
      {
        path: "enterprise/modern-dashboard",
        element: (
          <ProtectedRoute requiredRole="enterprise">
            <ModernDashboardEnterprisePage />
          </ProtectedRoute>
        )
      },
      {
        path: "courier/modern-dashboard",
        element: (
          <ProtectedRoute requiredRole="courier">
            <ModernDashboardCourierPage />
          </ProtectedRoute>
        )
      },

      // Routes de test pour développement
      {
        path: "test/client-dashboard",
        element: <ModernDashboardClientPage />
      },
      {
        path: "test/enterprise-dashboard",
        element: <ModernDashboardEnterprisePage />
      },
      {
        path: "test/courier-dashboard",
        element: <ModernDashboardCourierPage />
      },

      // Route de diagnostic pour le dashboard entreprise
      {
        path: "test/simple-enterprise-dashboard",
        element: <TestDashboardEnterprisePage />
      },

      // Route de test ultra-minimal
      {
        path: "test/minimal-enterprise",
        element: <MinimalTestPage />
      },

      // Route de test sans protection d'authentification
      {
        path: "test/enterprise-no-auth",
        element: <ModernDashboardEnterprisePage />
      },

      // Route d'urgence absolue
      {
        path: "emergency",
        element: <EmergencyTestPage />
      },

      // Dashboard entreprise qui fonctionne
      {
        path: "working-enterprise",
        element: <WorkingDashboardEnterprise />
      },
    ],
  }
]);

export default router;

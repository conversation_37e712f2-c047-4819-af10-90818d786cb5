import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { ThemeConfig, themeConfig } from '../configs/theme.config';

type ThemeAction = 
  | { type: 'SET_THEME_COLOR'; payload: string }
  | { type: 'SET_MODE'; payload: 'light' | 'dark' }
  | { type: 'SET_NAV_MODE'; payload: 'light' | 'dark' | 'themed' | 'transparent' }
  | { type: 'SET_LAYOUT_TYPE'; payload: string }
  | { type: 'TOGGLE_SIDEBAR'; payload?: boolean }
  | { type: 'SET_DIRECTION'; payload: 'ltr' | 'rtl' }
  | { type: 'RESET_THEME' };

interface ThemeContextType {
  theme: ThemeConfig;
  dispatch: React.Dispatch<ThemeAction>;
  toggleSidebar: () => void;
  setThemeColor: (color: string) => void;
  setMode: (mode: 'light' | 'dark') => void;
  setNavMode: (mode: 'light' | 'dark' | 'themed' | 'transparent') => void;
  setLayoutType: (type: string) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const themeReducer = (state: ThemeConfig, action: ThemeAction): ThemeConfig => {
  switch (action.type) {
    case 'SET_THEME_COLOR':
      return { ...state, themeColor: action.payload };
    case 'SET_MODE':
      return { ...state, mode: action.payload };
    case 'SET_NAV_MODE':
      return { ...state, navMode: action.payload };
    case 'SET_LAYOUT_TYPE':
      return { 
        ...state, 
        layout: { 
          ...state.layout, 
          type: action.payload as any 
        } 
      };
    case 'TOGGLE_SIDEBAR':
      return {
        ...state,
        layout: {
          ...state.layout,
          sideNavCollapse: action.payload !== undefined ? action.payload : !state.layout.sideNavCollapse,
        },
      };
    case 'SET_DIRECTION':
      return { ...state, direction: action.payload };
    case 'RESET_THEME':
      return themeConfig;
    default:
      return state;
  }
};

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [theme, dispatch] = useReducer(themeReducer, themeConfig);

  // Appliquer le mode sombre/clair au document
  useEffect(() => {
    if (theme.mode === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [theme.mode]);

  // Appliquer la direction au document
  useEffect(() => {
    document.documentElement.dir = theme.direction;
  }, [theme.direction]);

  const toggleSidebar = () => {
    dispatch({ type: 'TOGGLE_SIDEBAR' });
  };

  const setThemeColor = (color: string) => {
    dispatch({ type: 'SET_THEME_COLOR', payload: color });
  };

  const setMode = (mode: 'light' | 'dark') => {
    dispatch({ type: 'SET_MODE', payload: mode });
  };

  const setNavMode = (mode: 'light' | 'dark' | 'themed' | 'transparent') => {
    dispatch({ type: 'SET_NAV_MODE', payload: mode });
  };

  const setLayoutType = (type: string) => {
    dispatch({ type: 'SET_LAYOUT_TYPE', payload: type });
  };

  const value = {
    theme,
    dispatch,
    toggleSidebar,
    setThemeColor,
    setMode,
    setNavMode,
    setLayoutType,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeContext;

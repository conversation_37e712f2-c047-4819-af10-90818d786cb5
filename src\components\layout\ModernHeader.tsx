import React, { useState } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { HEADER_HEIGHT, SIDE_NAV_WIDTH, SIDE_NAV_COLLAPSED_WIDTH } from '../../configs/theme.config';
import {
  <PERSON>aS<PERSON>ch,
  FaBell,
  Fa<PERSON>ser,
  FaCog,
  FaSignOutAlt,
  FaSun,
  FaMoon,
  FaBars,
} from 'react-icons/fa';

interface ModernHeaderProps {
  className?: string;
}

const ModernHeader: React.FC<ModernHeaderProps> = ({ className }) => {
  const { theme, toggleSidebar, setMode } = useTheme();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const { sideNavCollapse } = theme.layout;

  const headerMargin = sideNavCollapse ? SIDE_NAV_COLLAPSED_WIDTH : SIDE_NAV_WIDTH;

  const headerClass = [
    'fixed top-0 right-0 z-30 transition-all duration-300 ease-in-out',
    'bg-white dark:bg-gray-900',
    'border-b border-gray-200 dark:border-gray-700',
    'shadow-sm',
    className || ''
  ].filter(Boolean).join(' ');

  const toggleDarkMode = () => {
    setMode(theme.mode === 'light' ? 'dark' : 'light');
  };

  const notifications = [
    { id: 1, title: 'Nouvelle demande', message: 'Une nouvelle demande de domiciliation', time: '5 min' },
    { id: 2, title: 'Document prêt', message: 'Votre attestation est prête', time: '1h' },
    { id: 3, title: 'Rappel', message: 'Renouvellement de domiciliation', time: '2h' },
  ];

  return (
    <header 
      className={headerClass}
      style={{ 
        left: headerMargin,
        height: HEADER_HEIGHT 
      }}
    >
      <div className="flex items-center justify-between h-full px-6">
        {/* Left side */}
        <div className="flex items-center space-x-4">
          <button
            onClick={toggleSidebar}
            className="lg:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
          >
            <FaBars className="text-gray-500 dark:text-gray-400" />
          </button>

          {/* Search */}
          <div className="hidden md:flex items-center">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm" />
              <input
                type="text"
                placeholder="Rechercher..."
                className="pl-10 pr-4 py-2 w-64 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>
          </div>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-4">
          {/* Dark mode toggle */}
          <button
            onClick={toggleDarkMode}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
          >
            {theme.mode === 'light' ? (
              <FaMoon className="text-gray-500 dark:text-gray-400" />
            ) : (
              <FaSun className="text-yellow-500" />
            )}
          </button>

          {/* Notifications */}
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200 relative"
            >
              <FaBell className="text-gray-500 dark:text-gray-400" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
            </button>

            {/* Notifications dropdown */}
            {showNotifications && (
              <div className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white">Notifications</h3>
                </div>
                <div className="max-h-64 overflow-y-auto">
                  {notifications.map((notification) => (
                    <div key={notification.id} className="p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-gray-800 dark:text-white text-sm">{notification.title}</p>
                          <p className="text-gray-600 dark:text-gray-300 text-sm">{notification.message}</p>
                        </div>
                        <span className="text-xs text-gray-500 dark:text-gray-400">{notification.time}</span>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="p-4">
                  <button className="w-full text-center text-blue-600 dark:text-blue-400 text-sm hover:underline">
                    Voir toutes les notifications
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* User menu */}
          <div className="relative">
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
            >
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <FaUser className="text-white text-sm" />
              </div>
              <span className="hidden md:block text-sm font-medium text-gray-700 dark:text-gray-300">
                Utilisateur
              </span>
            </button>

            {/* User dropdown */}
            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                <div className="py-2">
                  <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <FaUser className="mr-3" />
                    Profil
                  </button>
                  <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <FaCog className="mr-3" />
                    Paramètres
                  </button>
                  <hr className="my-2 border-gray-200 dark:border-gray-700" />
                  <button className="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <FaSignOutAlt className="mr-3" />
                    Déconnexion
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default ModernHeader;

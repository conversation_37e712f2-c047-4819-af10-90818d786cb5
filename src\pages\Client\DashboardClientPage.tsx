import { useState } from "react";
import {
  MdDash<PERSON>,
  Md<PERSON><PERSON>,
  MdNotifications,
  MdTrendingUp,
  Md<PERSON><PERSON><PERSON><PERSON><PERSON>,
  MdLocationCity,
} from "react-icons/md";
import NotreService from "../../components/client/NotreService";
import OrdersTable from "../../components/order/OrdersTable";
import NavBar from "../../components/NavBar";
import ClientSidebar from "../../components/client/ClientSidebar";
import { useAuthContext } from "../../contexts/AuthContext";
import ClientJuridicalServiceForm from "../../components/forms/ClientJuridicalServiceForm";

// Import all pages that will be displayed in the dashboard
import ClientProfilePage from "./ProfilePage";
import NotificationsPage from "./NotificationsPage";
import ClientComptabilitePage from "./ComptabilitePage";
import ClientOffresPage from "./OffresPage";
import DomiciliationManagement from "./DomiciliationManagement";

// Define the sections of the sidebar
const sections = [
  { icon: MdDashboard, label: "Dashboard", link: "/client/dashboard" },
  { icon: Md<PERSON><PERSON>, label: "Profile", link: "/client/profile" },
  { icon: MdNotifications, label: "Notifications", link: "/client/notifications" },
  { icon: MdTrendingUp, label: "Comptabilité", link: "/client/comptabilite" },
  { icon: MdLocalOffer, label: "Les offres", link: "/client/offres" },
  { icon: MdLocationCity, label: "Domiciliation", link: "/client/domiciliation" },
];

const DashboardClient = () => {
  const [currentPage, setCurrentPage] = useState("/client/dashboard");
  const [isLoading, setIsLoading] = useState(false);
  const [showJuridicalForm, setShowJuridicalForm] = useState(false);
  const { user } = useAuthContext();

  const handleLinkClick = (link: string) => {
    // Gérer le lien spécial pour le formulaire juridique
    if (link === "/client/juridique") {
      setShowJuridicalForm(true);
      return;
    }

    if (link !== currentPage) {
      setIsLoading(true);
      setCurrentPage(link);
      // Simuler un temps de chargement court
      setTimeout(() => {
        setIsLoading(false);
      }, 300);
    }
  };

  const handleCloseJuridicalForm = () => {
    setShowJuridicalForm(false);
  };

  // Fonction pour rendre le composant approprié en fonction de la page actuelle
  const renderCurrentPage = () => {
    switch (currentPage) {
      case "/client/dashboard":
        return (
          <div className="flex-1 p-4">
            <NotreService onServiceClick={handleLinkClick} />
            <div className="flex-1 p-4">
              <OrdersTable />
            </div>
          </div>
        );
      case "/client/profile":
        return <ClientProfilePage />;
      case "/client/notifications":
        return <NotificationsPage />;
      case "/client/comptabilite":
        return <ClientComptabilitePage />;
      case "/client/offres":
        return <ClientOffresPage />;
      case "/client/domiciliation":
        return <DomiciliationManagement />;
      default:
        return (
          <div className="flex-1 p-4">
            <NotreService onServiceClick={handleLinkClick} />
            <div className="flex-1 p-4">
              <OrdersTable />
            </div>
          </div>
        );
    }
  };

  // Si le formulaire juridique est affiché, on affiche seulement le formulaire
  if (showJuridicalForm) {
    return (
      <div className="bg-blue-100 min-h-screen">
        <ClientJuridicalServiceForm onClose={handleCloseJuridicalForm} />
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      <NavBar
        title="Dashboard"
        userName={user?.name}
        userImage={user?.profileImage}
        onProfileClick={handleLinkClick}
      />
      <div className="flex flex-1 overflow-auto">
        {/* Sidebar */}
        <ClientSidebar
          sections={sections}
          onLinkClick={handleLinkClick}
        />

        {/* Main Content */}
        <div className="flex-1 p-4 overflow-auto">
          {isLoading ? (
            <div className="w-full h-full flex items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="w-full bg-white rounded-lg shadow-md p-4">
              {renderCurrentPage()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DashboardClient;

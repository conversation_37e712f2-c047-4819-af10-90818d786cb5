import { formatDistance } from "./be-tarask/_lib/formatDistance.mjs";
import { formatLong } from "./be-tarask/_lib/formatLong.mjs";
import { formatRelative } from "./be-tarask/_lib/formatRelative.mjs";
import { localize } from "./be-tarask/_lib/localize.mjs";
import { match } from "./be-tarask/_lib/match.mjs";

/**
 * @category Locales
 * @summary Belarusian Classic locale.
 * @language Belarusian Classic
 * @iso-639-2 bel
 * <AUTHOR> [@nopears](https://github.com/nopears)
 */
export const beTarask = {
  code: "be-tarask",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default beTarask;

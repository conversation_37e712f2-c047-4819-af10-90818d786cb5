import React from 'react';
import { Document, Page, Text, View, StyleSheet, Image } from '@react-pdf/renderer';
import logo from '../../assets/logo/logo.png';

const styles = StyleSheet.create({
  page: { padding: 40, fontSize: 12, lineHeight: 1.6 },
  section: { marginBottom: 20 },
  title: { fontSize: 16, textAlign: 'center', marginBottom: 25, fontWeight: 'bold' },
  subtitle: { fontSize: 14, marginBottom: 15, fontWeight: 'bold', color: '#2563eb' },
  bold: { fontWeight: 'bold' },
  logo: {
    width: 100,
    height: 'auto',
    marginBottom: 20,
    alignSelf: 'left',
  },
  table: {
    display: 'table',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    marginBottom: 15
  },
  tableRow: {
    margin: 'auto',
    flexDirection: 'row'
  },
  tableCol: {
    width: '50%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderLeftWidth: 0,
    borderTopWidth: 0
  },
  tableCell: {
    margin: 'auto',
    marginTop: 5,
    marginBottom: 5,
    fontSize: 10,
    padding: 5
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 40,
    right: 40,
    textAlign: 'center',
    fontSize: 10,
    color: '#666'
  },
  contractSection: {
    marginTop: 30,
    padding: 15,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e5e7eb'
  }
});

interface DomiciliationPdfProps {
  formData: {
    nomCompletGerant: string;
    cinGerant: string;
    adresseGerant: string;
    nomEntreprise: string;
    ice: string;
    dateDebut: string;
    dateFin: string;
  };
}

const DomiciliationPdf: React.FC<DomiciliationPdfProps> = ({ formData }) => {
  const currentDate = new Date().toLocaleDateString('fr-FR');
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const adresseDomiciliation = "123 Avenue Mohammed V, Casablanca, Maroc"; // Adresse fixe de l'entreprise

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* LOGO */}
        <Image style={styles.logo} src={logo} />

        <Text style={styles.title}>
          Attestation de Domiciliation
        </Text>

        {/* Attestation principale */}
        <View style={styles.section}>
          <Text>
            Nous soussignés Société <Text style={styles.bold}>MACHROUHI AFFAIRES</Text>, attestons par la présente que la société <Text style={styles.bold}>{formData.nomEntreprise}</Text>, représentée par son gérant <Text style={styles.bold}>{formData.nomCompletGerant}</Text> (CIN: {formData.cinGerant}), a domicilié son adresse fiscale dans nos locaux situés à <Text style={styles.bold}>{adresseDomiciliation}</Text> pour une période allant du <Text style={styles.bold}>{formatDate(formData.dateDebut)}</Text> au <Text style={styles.bold}>{formatDate(formData.dateFin)}</Text>.
          </Text>
        </View>

        <View style={styles.section}>
          <Text>
            Nous déclarons en outre avoir pris connaissance qu'en application des dispositions de l'article 93 du code de recouvrement des créances publiques, les rôles des impôts, états de produits et autres titres de perception régulièrement émis ont exécution contre les redevables qui y sont inscrits, toutes autres personnes auprès desquelles les redevables ont élu domicile fiscal, avec leur accord.
          </Text>
        </View>

        <View style={styles.section}>
          <Text>
            Les personnes auprès desquelles les redevables ont élu domicile fiscal avec leur accord peuvent, de ce fait, faire l'objet d'action en recouvrement au même titre que les redevables à raison des créances dues au titre de l'activité concernée par la durée de la domiciliation.
          </Text>
        </View>

        <View style={styles.section}>
          <Text>
            En foi de quoi, la présente attestation est délivrée pour permettre à l'entreprise de procéder aux formalités administratives.
          </Text>
        </View>

        <View style={styles.section}>
          <Text>Fait à Casablanca le : <Text style={styles.bold}>{currentDate}</Text></Text>
        </View>

        <View style={styles.section}>
          <Text>Gérant : <Text style={styles.bold}>{formData.nomCompletGerant}</Text></Text>
        </View>

      </Page>
    </Document>
  );
};

export default DomiciliationPdf;

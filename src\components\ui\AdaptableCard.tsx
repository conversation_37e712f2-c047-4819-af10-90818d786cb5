import React from 'react';

interface AdaptableCardProps {
  className?: string;
  children: React.ReactNode;
  bodyClass?: string;
  leftSideBorder?: boolean;
  rightSideBorder?: boolean;
  divider?: boolean;
  shadow?: boolean;
  isLastChild?: boolean;
  bordered?: boolean;
  clickable?: boolean;
  onClick?: () => void;
}

const AdaptableCard: React.FC<AdaptableCardProps> = ({
  className,
  children,
  bodyClass,
  leftSideBorder,
  rightSideBorder,
  divider,
  shadow = true,
  isLastChild,
  bordered = true,
  clickable,
  onClick,
  ...rest
}) => {
  const cardClasses = [
    'card bg-white dark:bg-gray-800 rounded-lg',
    bordered ? 'border border-gray-200 dark:border-gray-600' : '',
    shadow ? 'shadow-sm' : '',
    leftSideBorder ? 'border-l-4 border-l-blue-500' : '',
    rightSideBorder ? 'border-r-4 border-r-blue-500' : '',
    divider && !isLastChild ? 'border-b border-gray-200 dark:border-gray-600' : '',
    clickable ? 'cursor-pointer hover:shadow-md transition-shadow duration-200' : '',
    className || ''
  ].filter(Boolean).join(' ');

  const bodyClasses = [
    'card-body p-6',
    bodyClass || ''
  ].filter(Boolean).join(' ');

  return (
    <div className={cardClasses} onClick={onClick} {...rest}>
      <div className={bodyClasses}>
        {children}
      </div>
    </div>
  );
};

export default AdaptableCard;

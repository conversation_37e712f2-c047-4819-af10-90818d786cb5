import { formatDistance } from "./bn/_lib/formatDistance.mjs";
import { formatLong } from "./bn/_lib/formatLong.mjs";
import { formatRelative } from "./bn/_lib/formatRelative.mjs";
import { localize } from "./bn/_lib/localize.mjs";
import { match } from "./bn/_lib/match.mjs";

/**
 * @category Locales
 * @summary Bengali locale.
 * @language Bengali
 * @iso-639-2 ben
 * <AUTHOR> [@touh<PERSON><PERSON><PERSON>](https://github.com/touhid<PERSON>man)
 * <AUTHOR> [@nutboltu](https://github.com/nutboltu)
 */
export const bn = {
  code: "bn",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default bn;

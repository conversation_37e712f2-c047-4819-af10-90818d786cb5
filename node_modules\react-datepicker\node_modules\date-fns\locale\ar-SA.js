import { formatDistance } from "./ar-SA/_lib/formatDistance.js";
import { formatLong } from "./ar-SA/_lib/formatLong.js";
import { formatRelative } from "./ar-SA/_lib/formatRelative.js";
import { localize } from "./ar-SA/_lib/localize.js";
import { match } from "./ar-SA/_lib/match.js";

/**
 * @category Locales
 * @summary Arabic locale (Sauid Arabic).
 * @language Arabic
 * @iso-639-2 ara
 * <AUTHOR> [@dalwadani](https://github.com/dalwadani)
 */
export const arSA = {
  code: "ar-SA",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default arSA;

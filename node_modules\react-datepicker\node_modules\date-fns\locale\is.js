import { formatDistance } from "./is/_lib/formatDistance.js";
import { formatLong } from "./is/_lib/formatLong.js";
import { formatRelative } from "./is/_lib/formatRelative.js";
import { localize } from "./is/_lib/localize.js";
import { match } from "./is/_lib/match.js";

/**
 * @category Locales
 * @summary Icelandic locale.
 * @language Icelandic
 * @iso-639-2 isl
 * <AUTHOR> [@derekblank](https://github.com/derekblank)
 * <AUTHOR> Ýmir [@lamayg](https://github.com/lamayg)
 */
export const is = {
  code: "is",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default is;

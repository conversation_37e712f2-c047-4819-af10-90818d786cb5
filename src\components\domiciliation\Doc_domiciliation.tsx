import React from 'react';
import { Document, Page, Text, View, StyleSheet, Image } from '@react-pdf/renderer';
import logo from '../assets/logo.png';

const styles = StyleSheet.create({
  page: { padding: 40, fontSize: 12, lineHeight: 1.6 },
  section: { marginBottom: 20 },
  title: { fontSize: 14, textAlign: 'center', marginBottom: 20, fontWeight: 'bold' },
  bold: { fontWeight: 'bold' },
  logo: {
    width: 100,
    height: 'auto',
    marginBottom: 20,
    alignSelf: 'center',
  },
});

type Props = {
  XCENTRE: string;
  XSTE: string;
  XTYPE: string;
  XSIEGE2: string;
  xdate1: string;
  xdate2: string;
  XVILLE: string;
  Xnom1: string;
  XSIEGE1: string;
  XICE2: string;
  Xnom2: string;
  CIN2: string;
  XADRESSE2: string;
  XMOIS: string;
  XDATE1: string;
  XDATE2: string;
  XCIN2: string;
  XICE1: string;
  XIF1: string;
  XPATANTE1: string;
  XRC1: string;
  XTEL1: string;
  XFIX1: string;
  XADRESSE1: string;
};

const AttestationPdf: React.FC<Props> = ({
  XCENTRE,
  XSTE,
  XTYPE,
  XSIEGE2,
  xdate1,
  xdate2,
  XVILLE,
  Xnom1,
  XSIEGE1,
  XICE2,
  Xnom2,
  CIN2,
  XADRESSE2,
  XMOIS,
  XDATE1,
  XDATE2,
  XCIN2,
  XICE1,
  XIF1,
  XPATANTE1,
  XRC1,
  XTEL1,
  XFIX1,
  XADRESSE1
}) => (
  <Document>
    <Page size="A4" style={styles.page}>
      <Image style={styles.logo} src={logo} />
      <Text style={styles.title}>Attestation de domiciliation</Text>

      <View style={styles.section}>
        <Text>
          Nous soussignés <Text style={styles.bold}>{XCENTRE}</Text>, attestant par la présente que la société <Text style={styles.bold}>{XSTE}</Text> <Text>{XTYPE}</Text>, a domicilié son adresse fiscale dans nos locaux situés à <Text style={styles.bold}>{XSIEGE2}</Text> pour une période allant du <Text style={styles.bold}>{xdate1}</Text> au <Text style={styles.bold}>{xdate2}</Text>.
        </Text>
      </View>

      <View style={styles.section}>
        <Text>
          Nous déclarons en outre avoir pris connaissance qu’en application des dispositions de l’article 93 du code de recouvrement des créances publique, les rôles des impôts, états de produits et autre titres de perception régulièrement émis ont exécutions contre les redevables qui y sont inscrits, toutes autres personnes auprès desquelles les redevables ont élu domicile fiscal, avec leur accord.
        </Text>
      </View>

      <View style={styles.section}>
        <Text>
          Les personnes auprès desquelles les redevables ont élu domicile fiscal avec leur accord, peuvent, de ce fait, faire l’objet d’action en recouvrement au même titre que les redevables à raison des créances dues au titre de l’activité concernée par la durée de la domiciliation.
        </Text>
      </View>

      <View style={styles.section}>
        <Text>
          En foi de quoi, la présente attestation est délivrée pour lui permettre de procéder aux formalités distractives.
        </Text>
      </View>

      <View style={styles.section}>
        <Text>Fait à <Text style={styles.bold}>{XVILLE}</Text></Text>
      </View>

      <View style={styles.section}>
        <Text>Gérant : <Text style={styles.bold}>{Xnom1}</Text></Text>
      </View>

      <Text style={styles.title}>Contrat de domiciliation</Text>

      <View style={styles.section}>
        <Text>Le domiciliataire</Text>
        <Text>La société <Text style={styles.bold}>{XCENTRE}</Text> SIS AU : <Text style={styles.bold}>{XSIEGE1}</Text> – Maroc.</Text>
        <Text>Représentée par : <Text style={styles.bold}>{Xnom1}</Text>, titulaire de la CIN N° <Text style={styles.bold}>{XCIN2}</Text>, {XADRESSE1} en sa qualité de gérant de la société</Text>
      </View>

      <View style={styles.section}>
        <Text>Et le domicilié : La société <Text style={styles.bold}>{XSTE}</Text>, ICE : <Text style={styles.bold}>{XICE2}</Text></Text>
        <Text>Représentée par : <Text style={styles.bold}>{Xnom2}</Text>, titulaire de la CIN N° <Text style={styles.bold}>{CIN2}</Text>, {XADRESSE2}</Text>
      </View>

      <View style={styles.section}>
        <Text>ARTICLE 1 : OBJET</Text>
        <Text>
          Le présent contrat est conclu pour la domiciliation du siège de la société : <Text style={styles.bold}>{XSTE}</Text> titulaire de l’ICE N° <Text style={styles.bold}>{XICE2}</Text>, en application des dispositions de l’article 2.544 de la loi 15.95 portant code de commerce.
        </Text>
      </View>

      <View style={styles.section}>
        <Text>ARTICLE 4 : DUREE DU CONTRAT</Text>
        <Text>
          Le présent contrat de domiciliation est conclu pour une durée de <Text style={styles.bold}>{XMOIS}</Text>, qui commencera le <Text style={styles.bold}>{XDATE1}</Text> et se terminera le <Text style={styles.bold}>{XDATE2}</Text>.
        </Text>
      </View>

      <View style={styles.section}>
        <Text>ARTICLE 7 : ELECTION DE DOMICILE</Text>
        <Text>Les parties élisent domicile à leur siège social :</Text>
        <Text>Le domiciliataire : <Text style={styles.bold}>{XCENTRE}</Text></Text>
        <Text>Le domicilié : <Text style={styles.bold}>{XSTE}</Text></Text>
      </View>

      <View style={styles.section}>
        <Text>ARTICLE 8 : PROCURATION SPECIALE</Text>
        <Text>
          <Text style={styles.bold}>{Xnom2}</Text> titulaire de la CIN N° <Text style={styles.bold}>{XCIN2}</Text>, agissant en qualité d’associé principal de la société «<Text style={styles.bold}>{XSTE}</Text>» {XICE2}, donne par la présente procuration au <Text style={styles.bold}>{XCENTRE}</Text>, pour la réception de toutes sortes de notifications en notre nom pendant la durée du contrat.
        </Text>
      </View>

      <View style={styles.section}>
        <Text>A <Text style={styles.bold}>{XVILLE}</Text> LE :</Text>
      </View>

      <View style={styles.section}>
        <Text>SIGNATURE DOMICILIATAIRE</Text>
        <Text>SIGNATURE DOMICILIE</Text>
      </View>

      <View style={styles.section}>
        <Text><Text style={styles.bold}>{XCENTRE}</Text> SIS AU : <Text style={styles.bold}>{XSIEGE1}</Text> – Maroc</Text>
        <Text>IF : <Text style={styles.bold}>{XIF1}</Text> PATANTE : <Text style={styles.bold}>{XPATANTE1}</Text> RC : <Text style={styles.bold}>{XRC1}</Text> ICE : <Text style={styles.bold}>{XICE1}</Text></Text>
        <Text>TEL : <Text style={styles.bold}>{XTEL1}</Text> FIX : <Text style={styles.bold}>{XFIX1}</Text></Text>
      </View>
    </Page>
  </Document>
);

export default AttestationPdf;

import React from 'react';
import { Document, Page, Text, View, StyleSheet, Image } from '@react-pdf/renderer';
import logo from '../../assets/logo/logo.png';

const styles = StyleSheet.create({
  page: { padding: 40, fontSize: 12, lineHeight: 1.6 },
  section: { marginBottom: 20 },
  title: { fontSize: 14, textAlign: 'center', marginBottom: 20, fontWeight: 'bold' },
  bold: { fontWeight: 'bold' },
  logo: {
    width: 100,
    height: 'auto',
    marginBottom: 20,
    alignSelf: 'center',
  },
});

interface DomiciliationData {
  // Informations du domiciliataire (MACHROUHI AFFAIRES)
  XCENTRE: string;           // Nom de l'entreprise domiciliataire
  XSIEGE1: string;           // Adresse du domiciliataire
  Xnom1: string;             // Nom du gérant domiciliataire
  XCIN2: string;             // CIN du gérant domiciliataire
  XADRESSE1: string;         // Adresse du gérant domiciliataire
  XICE1: string;             // ICE du domiciliataire
  XIF1: string;              // IF du domiciliataire
  XPATANTE1: string;         // Patente du domiciliataire
  XRC1: string;              // RC du domiciliataire
  XTEL1: string;             // Téléphone du domiciliataire
  XFIX1: string;             // Fixe du domiciliataire

  // Informations du domicilié (client)
  XSTE: string;              // Nom de l'entreprise domiciliée
  XTYPE: string;             // Type de société
  XICE2: string;             // ICE du domicilié
  Xnom2: string;             // Nom du gérant domicilié
  CIN2: string;              // CIN du gérant domicilié
  XADRESSE2: string;         // Adresse du gérant domicilié

  // Informations de domiciliation
  XSIEGE2: string;           // Adresse de domiciliation
  xdate1: string;            // Date de début (format affiché)
  xdate2: string;            // Date de fin (format affiché)
  XDATE1: string;            // Date de début (format contrat)
  XDATE2: string;            // Date de fin (format contrat)
  XMOIS: string;             // Durée en mois
  XVILLE: string;            // Ville
}

const AttestationPdf: React.FC<Props> = ({
  XCENTRE,
  XSTE,
  XTYPE,
  XSIEGE2,
  xdate1,
  xdate2,
  XVILLE,
  Xnom1,
  XSIEGE1,
  XICE2,
  Xnom2,
  CIN2,
  XADRESSE2,
  XMOIS,
  XDATE1,
  XDATE2,
  XCIN2,
  XICE1,
  XIF1,
  XPATANTE1,
  XRC1,
  XTEL1,
  XFIX1,
  XADRESSE1
}) => (
  <Document>
    <Page size="A4" style={styles.page}>
      <Image style={styles.logo} src={logo} />
      <Text style={styles.title}>Attestation de domiciliation</Text>

      <View style={styles.section}>
        <Text>
          Nous soussignés <Text style={styles.bold}>{XCENTRE}</Text>, attestant par la présente que la société <Text style={styles.bold}>{XSTE}</Text> <Text>{XTYPE}</Text>, a domicilié son adresse fiscale dans nos locaux situés à <Text style={styles.bold}>{XSIEGE2}</Text> pour une période allant du <Text style={styles.bold}>{xdate1}</Text> au <Text style={styles.bold}>{xdate2}</Text>.
        </Text>
      </View>

      <View style={styles.section}>
        <Text>
          Nous déclarons en outre avoir pris connaissance qu’en application des dispositions de l’article 93 du code de recouvrement des créances publiques, les rôles des impôts, états de produits et autres titres de perception régulièrement émis ont exécution contre les redevables qui y sont inscrits, toutes autres personnes auprès desquelles les redevables ont élu domicile fiscal, avec leur accord.
        </Text>
      </View>

      <View style={styles.section}>
        <Text>
          Les personnes auprès desquelles les redevables ont élu domicile fiscal avec leur accord, peuvent, de ce fait, faire l’objet d’action en recouvrement au même titre que les redevables à raison des créances dues au titre de l’activité concernée par la durée de la domiciliation.
        </Text>
      </View>

      <View style={styles.section}>
        <Text>
          En foi de quoi, la présente attestation est délivrée pour lui permettre de procéder aux formalités administratives.
        </Text>
      </View>

      <View style={styles.section}>
        <Text>Fait à <Text style={styles.bold}>{XVILLE}</Text></Text>
      </View>

      <View style={styles.section}>
        <Text>Gérant : <Text style={styles.bold}>{Xnom1}</Text></Text>
      </View>

      <Text style={styles.title}>Contrat de domiciliation</Text>

      <View style={styles.section}>
        <Text>Le domiciliataire</Text>
        <Text>La société <Text style={styles.bold}>{XCENTRE}</Text> SISE AU : <Text style={styles.bold}>{XSIEGE1}</Text> – Maroc.</Text>
        <Text>Représentée par : <Text style={styles.bold}>{Xnom1}</Text>, titulaire de la CIN N° <Text style={styles.bold}>{XCIN2}</Text>, {XADRESSE1} en sa qualité de gérant de la société</Text>
      </View>

      <View style={styles.section}>
        <Text>Et le domicilié : La société <Text style={styles.bold}>{XSTE}</Text>, ICE : <Text style={styles.bold}>{XICE2}</Text></Text>
        <Text>Représentée par : <Text style={styles.bold}>{Xnom2}</Text>, titulaire de la CIN N° <Text style={styles.bold}>{CIN2}</Text>, {XADRESSE2}</Text>
      </View>

      <View style={styles.section}>
        <Text>ARTICLE 1 : OBJET</Text>
        <Text>
          Le présent contrat est conclu pour la domiciliation du siège de la société : <Text style={styles.bold}>{XSTE}</Text> titulaire de l’ICE N° <Text style={styles.bold}>{XICE2}</Text>, en application des dispositions de l’article 2.544 de la loi 15.95 portant code de commerce.
        </Text>
      </View>

      <View style={styles.section}>
        <Text>ARTICLE 4 : DUREE DU CONTRAT</Text>
        <Text>
          Le présent contrat de domiciliation est conclu pour une durée de <Text style={styles.bold}>{XMOIS}</Text>, qui commencera le <Text style={styles.bold}>{XDATE1}</Text> et se terminera le <Text style={styles.bold}>{XDATE2}</Text>.
        </Text>
      </View>

      <View style={styles.section}>
        <Text>ARTICLE 7 : ELECTION DE DOMICILE</Text>
        <Text>Les parties élisent domicile à leur siège social :</Text>
        <Text>Le domiciliataire : <Text style={styles.bold}>{XCENTRE}</Text></Text>
        <Text>Le domicilié : <Text style={styles.bold}>{XSTE}</Text></Text>
      </View>

      <View style={styles.section}>
        <Text>ARTICLE 8 : PROCURATION SPECIALE</Text>
        <Text>
          <Text style={styles.bold}>{Xnom2}</Text> titulaire de la CIN N° <Text style={styles.bold}>{XCIN2}</Text>, agissant en qualité d’associé principal de la société «<Text style={styles.bold}>{XSTE}</Text>» {XICE2}, donne par la présente procuration au <Text style={styles.bold}>{XCENTRE}</Text>, pour la réception de toutes sortes de notifications en notre nom pendant la durée du contrat.
        </Text>
      </View>

      <View style={styles.section}>
        <Text>A <Text style={styles.bold}>{XVILLE}</Text> LE :</Text>
      </View>

      <View style={styles.section}>
        <Text>SIGNATURE DOMICILIATAIRE</Text>
        <Text>SIGNATURE DOMICILIÉ</Text>
      </View>

      <View style={styles.section}>
        <Text><Text style={styles.bold}>{XCENTRE}</Text> SISE AU : <Text style={styles.bold}>{XSIEGE1}</Text> – Maroc</Text>
        <Text>IF : <Text style={styles.bold}>{XIF1}</Text> PATENTE : <Text style={styles.bold}>{XPATANTE1}</Text> RC : <Text style={styles.bold}>{XRC1}</Text> ICE : <Text style={styles.bold}>{XICE1}</Text></Text>
        <Text>TÉL : <Text style={styles.bold}>{XTEL1}</Text> FIX : <Text style={styles.bold}>{XFIX1}</Text></Text>
      </View>
    </Page>
  </Document>
);

export default AttestationPdf;

(()=>{var B;function I(G){return I=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},I(G)}function x(G,H){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);H&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(G,Y).enumerable})),J.push.apply(J,X)}return J}function q(G){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?x(Object(J),!0).forEach(function(X){E(G,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):x(Object(J)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(J,X))})}return G}function E(G,H,J){if(H=N(H),H in G)Object.defineProperty(G,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[H]=J;return G}function N(G){var H=z(G,"string");return I(H)=="symbol"?H:String(H)}function z(G,H){if(I(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var X=J.call(G,H||"default");if(I(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var W=Object.defineProperty,JG=function G(H,J){for(var X in J)W(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Y(Z){return J[X]=function(){return Z}}})},S={lessThanXSeconds:{one:"mai pu\u021Bin de o secund\u0103",other:"mai pu\u021Bin de {{count}} secunde"},xSeconds:{one:"1 secund\u0103",other:"{{count}} secunde"},halfAMinute:"jum\u0103tate de minut",lessThanXMinutes:{one:"mai pu\u021Bin de un minut",other:"mai pu\u021Bin de {{count}} minute"},xMinutes:{one:"1 minut",other:"{{count}} minute"},aboutXHours:{one:"circa 1 or\u0103",other:"circa {{count}} ore"},xHours:{one:"1 or\u0103",other:"{{count}} ore"},xDays:{one:"1 zi",other:"{{count}} zile"},aboutXWeeks:{one:"circa o s\u0103pt\u0103m\xE2n\u0103",other:"circa {{count}} s\u0103pt\u0103m\xE2ni"},xWeeks:{one:"1 s\u0103pt\u0103m\xE2n\u0103",other:"{{count}} s\u0103pt\u0103m\xE2ni"},aboutXMonths:{one:"circa 1 lun\u0103",other:"circa {{count}} luni"},xMonths:{one:"1 lun\u0103",other:"{{count}} luni"},aboutXYears:{one:"circa 1 an",other:"circa {{count}} ani"},xYears:{one:"1 an",other:"{{count}} ani"},overXYears:{one:"peste 1 an",other:"peste {{count}} ani"},almostXYears:{one:"aproape 1 an",other:"aproape {{count}} ani"}},D=function G(H,J,X){var Y,Z=S[H];if(typeof Z==="string")Y=Z;else if(J===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(J));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return"\xEEn "+Y;else return Y+" \xEEn urm\u0103";return Y};function $(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):G.defaultWidth,X=G.formats[J]||G.formats[G.defaultWidth];return X}}var M={full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"dd.MM.yyyy"},R={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},L={full:"{{date}} 'la' {{time}}",long:"{{date}} 'la' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:$({formats:M,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"eeee 'trecut\u0103 la' p",yesterday:"'ieri la' p",today:"'ast\u0103zi la' p",tomorrow:"'m\xE2ine la' p",nextWeek:"eeee 'viitoare la' p",other:"P"},w=function G(H,J,X,Y){return j[H]};function O(G){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Y;if(X==="formatting"&&G.formattingValues){var Z=G.defaultFormattingWidth||G.defaultWidth,C=J!==null&&J!==void 0&&J.width?String(J.width):Z;Y=G.formattingValues[C]||G.formattingValues[Z]}else{var T=G.defaultWidth,A=J!==null&&J!==void 0&&J.width?String(J.width):G.defaultWidth;Y=G.values[A]||G.values[T]}var U=G.argumentCallback?G.argumentCallback(H):H;return Y[U]}}var _={narrow:["\xCE","D"],abbreviated:["\xCE.d.C.","D.C."],wide:["\xCEnainte de Cristos","Dup\u0103 Cristos"]},f={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["primul trimestru","al doilea trimestru","al treilea trimestru","al patrulea trimestru"]},v={narrow:["I","F","M","A","M","I","I","A","S","O","N","D"],abbreviated:["ian","feb","mar","apr","mai","iun","iul","aug","sep","oct","noi","dec"],wide:["ianuarie","februarie","martie","aprilie","mai","iunie","iulie","august","septembrie","octombrie","noiembrie","decembrie"]},F={narrow:["d","l","m","m","j","v","s"],short:["du","lu","ma","mi","jo","vi","s\xE2"],abbreviated:["dum","lun","mar","mie","joi","vin","s\xE2m"],wide:["duminic\u0103","luni","mar\u021Bi","miercuri","joi","vineri","s\xE2mb\u0103t\u0103"]},P={narrow:{am:"a",pm:"p",midnight:"mn",noon:"ami",morning:"dim",afternoon:"da",evening:"s",night:"n"},abbreviated:{am:"AM",pm:"PM",midnight:"miezul nop\u021Bii",noon:"amiaz\u0103",morning:"diminea\u021B\u0103",afternoon:"dup\u0103-amiaz\u0103",evening:"sear\u0103",night:"noapte"},wide:{am:"a.m.",pm:"p.m.",midnight:"miezul nop\u021Bii",noon:"amiaz\u0103",morning:"diminea\u021B\u0103",afternoon:"dup\u0103-amiaz\u0103",evening:"sear\u0103",night:"noapte"}},k={narrow:{am:"a",pm:"p",midnight:"mn",noon:"amiaz\u0103",morning:"diminea\u021B\u0103",afternoon:"dup\u0103-amiaz\u0103",evening:"sear\u0103",night:"noapte"},abbreviated:{am:"AM",pm:"PM",midnight:"miezul nop\u021Bii",noon:"amiaz\u0103",morning:"diminea\u021B\u0103",afternoon:"dup\u0103-amiaz\u0103",evening:"sear\u0103",night:"noapte"},wide:{am:"a.m.",pm:"p.m.",midnight:"miezul nop\u021Bii",noon:"amiaz\u0103",morning:"diminea\u021B\u0103",afternoon:"dup\u0103-amiaz\u0103",evening:"sear\u0103",night:"noapte"}},b=function G(H,J){return String(H)},h={ordinalNumber:b,era:O({values:_,defaultWidth:"wide"}),quarter:O({values:f,defaultWidth:"wide",argumentCallback:function G(H){return H-1}}),month:O({values:v,defaultWidth:"wide"}),day:O({values:F,defaultWidth:"wide"}),dayPeriod:O({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function Q(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Y=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Z=H.match(Y);if(!Z)return null;var C=Z[0],T=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],A=Array.isArray(T)?c(T,function(K){return K.test(C)}):m(T,function(K){return K.test(C)}),U;U=G.valueCallback?G.valueCallback(A):A,U=J.valueCallback?J.valueCallback(U):U;var HG=H.slice(C.length);return{value:U,rest:HG}}}function m(G,H){for(var J in G)if(Object.prototype.hasOwnProperty.call(G,J)&&H(G[J]))return J;return}function c(G,H){for(var J=0;J<G.length;J++)if(H(G[J]))return J;return}function y(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(G.matchPattern);if(!X)return null;var Y=X[0],Z=H.match(G.parsePattern);if(!Z)return null;var C=G.valueCallback?G.valueCallback(Z[0]):Z[0];C=J.valueCallback?J.valueCallback(C):C;var T=H.slice(Y.length);return{value:C,rest:T}}}var p=/^(\d+)?/i,d=/\d+/i,g={narrow:/^(Î|D)/i,abbreviated:/^(Î\.?\s?d\.?\s?C\.?|Î\.?\s?e\.?\s?n\.?|D\.?\s?C\.?|e\.?\s?n\.?)/i,wide:/^(Înainte de Cristos|Înaintea erei noastre|După Cristos|Era noastră)/i},u={any:[/^ÎC/i,/^DC/i],wide:[/^(Înainte de Cristos|Înaintea erei noastre)/i,/^(După Cristos|Era noastră)/i]},l={narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^trimestrul [1234]/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[ifmaasond]/i,abbreviated:/^(ian|feb|mar|apr|mai|iun|iul|aug|sep|oct|noi|dec)/i,wide:/^(ianuarie|februarie|martie|aprilie|mai|iunie|iulie|august|septembrie|octombrie|noiembrie|decembrie)/i},s={narrow:[/^i/i,/^f/i,/^m/i,/^a/i,/^m/i,/^i/i,/^i/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ia/i,/^f/i,/^mar/i,/^ap/i,/^mai/i,/^iun/i,/^iul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},o={narrow:/^[dlmjvs]/i,short:/^(d|l|ma|mi|j|v|s)/i,abbreviated:/^(dum|lun|mar|mie|jo|vi|sâ)/i,wide:/^(duminica|luni|marţi|miercuri|joi|vineri|sâmbătă)/i},r={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^d/i,/^l/i,/^ma/i,/^mi/i,/^j/i,/^v/i,/^s/i]},a={narrow:/^(a|p|mn|a|(dimineaţa|după-amiaza|seara|noaptea))/i,any:/^([ap]\.?\s?m\.?|miezul nopții|amiaza|(dimineaţa|după-amiaza|seara|noaptea))/i},e={any:{am:/^a/i,pm:/^p/i,midnight:/^mn/i,noon:/amiaza/i,morning:/dimineaţa/i,afternoon:/după-amiaza/i,evening:/seara/i,night:/noaptea/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:d,valueCallback:function G(H){return parseInt(H,10)}}),era:Q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:Q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},GG={code:"ro",formatDistance:D,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=q(q({},window.dateFns),{},{locale:q(q({},(B=window.dateFns)===null||B===void 0?void 0:B.locale),{},{ro:GG})})})();

//# debugId=192070FD4982620A64756E2164756E21

(()=>{var $;function U(B){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},U(B)}function N(B,C){var G=Object.keys(B);if(Object.getOwnPropertySymbols){var H=Object.getOwnPropertySymbols(B);C&&(H=H.filter(function(J){return Object.getOwnPropertyDescriptor(B,J).enumerable})),G.push.apply(G,H)}return G}function Q(B){for(var C=1;C<arguments.length;C++){var G=arguments[C]!=null?arguments[C]:{};C%2?N(Object(G),!0).forEach(function(H){W(B,H,G[H])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(G)):N(Object(G)).forEach(function(H){Object.defineProperty(B,H,Object.getOwnPropertyDescriptor(G,H))})}return B}function W(B,C,G){if(C=E(C),C in B)Object.defineProperty(B,C,{value:G,enumerable:!0,configurable:!0,writable:!0});else B[C]=G;return B}function E(B){var C=S(B,"string");return U(C)=="symbol"?C:String(C)}function S(B,C){if(U(B)!="object"||!B)return B;var G=B[Symbol.toPrimitive];if(G!==void 0){var H=G.call(B,C||"default");if(U(H)!="object")return H;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(B)}var D=Object.defineProperty,HB=function B(C,G){for(var H in G)D(C,H,{get:G[H],enumerable:!0,configurable:!0,set:function J(X){return G[H]=function(){return X}}})},z={lessThanXSeconds:{standalone:{one:"weniger als 1 Sekunde",other:"weniger als {{count}} Sekunden"},withPreposition:{one:"weniger als 1 Sekunde",other:"weniger als {{count}} Sekunden"}},xSeconds:{standalone:{one:"1 Sekunde",other:"{{count}} Sekunden"},withPreposition:{one:"1 Sekunde",other:"{{count}} Sekunden"}},halfAMinute:{standalone:"eine halbe Minute",withPreposition:"einer halben Minute"},lessThanXMinutes:{standalone:{one:"weniger als 1 Minute",other:"weniger als {{count}} Minuten"},withPreposition:{one:"weniger als 1 Minute",other:"weniger als {{count}} Minuten"}},xMinutes:{standalone:{one:"1 Minute",other:"{{count}} Minuten"},withPreposition:{one:"1 Minute",other:"{{count}} Minuten"}},aboutXHours:{standalone:{one:"etwa 1 Stunde",other:"etwa {{count}} Stunden"},withPreposition:{one:"etwa 1 Stunde",other:"etwa {{count}} Stunden"}},xHours:{standalone:{one:"1 Stunde",other:"{{count}} Stunden"},withPreposition:{one:"1 Stunde",other:"{{count}} Stunden"}},xDays:{standalone:{one:"1 Tag",other:"{{count}} Tage"},withPreposition:{one:"1 Tag",other:"{{count}} Tagen"}},aboutXWeeks:{standalone:{one:"etwa 1 Woche",other:"etwa {{count}} Wochen"},withPreposition:{one:"etwa 1 Woche",other:"etwa {{count}} Wochen"}},xWeeks:{standalone:{one:"1 Woche",other:"{{count}} Wochen"},withPreposition:{one:"1 Woche",other:"{{count}} Wochen"}},aboutXMonths:{standalone:{one:"etwa 1 Monat",other:"etwa {{count}} Monate"},withPreposition:{one:"etwa 1 Monat",other:"etwa {{count}} Monaten"}},xMonths:{standalone:{one:"1 Monat",other:"{{count}} Monate"},withPreposition:{one:"1 Monat",other:"{{count}} Monaten"}},aboutXYears:{standalone:{one:"etwa 1 Jahr",other:"etwa {{count}} Jahre"},withPreposition:{one:"etwa 1 Jahr",other:"etwa {{count}} Jahren"}},xYears:{standalone:{one:"1 Jahr",other:"{{count}} Jahre"},withPreposition:{one:"1 Jahr",other:"{{count}} Jahren"}},overXYears:{standalone:{one:"mehr als 1 Jahr",other:"mehr als {{count}} Jahre"},withPreposition:{one:"mehr als 1 Jahr",other:"mehr als {{count}} Jahren"}},almostXYears:{standalone:{one:"fast 1 Jahr",other:"fast {{count}} Jahre"},withPreposition:{one:"fast 1 Jahr",other:"fast {{count}} Jahren"}}},M=function B(C,G,H){var J,X=H!==null&&H!==void 0&&H.addSuffix?z[C].withPreposition:z[C].standalone;if(typeof X==="string")J=X;else if(G===1)J=X.one;else J=X.other.replace("{{count}}",String(G));if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)return"in "+J;else return"vor "+J;return J};function K(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=C.width?String(C.width):B.defaultWidth,H=B.formats[G]||B.formats[B.defaultWidth];return H}}var R={full:"EEEE, do MMMM y",long:"do MMMM y",medium:"do MMM y",short:"dd.MM.y"},L={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},V={full:"{{date}} 'um' {{time}}",long:"{{date}} 'um' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},j={date:K({formats:R,defaultWidth:"full"}),time:K({formats:L,defaultWidth:"full"}),dateTime:K({formats:V,defaultWidth:"full"})},w={lastWeek:"'letzten' eeee 'um' p",yesterday:"'gestern um' p",today:"'heute um' p",tomorrow:"'morgen um' p",nextWeek:"eeee 'um' p",other:"P"},_=function B(C,G,H,J){return w[C]};function I(B){return function(C,G){var H=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",J;if(H==="formatting"&&B.formattingValues){var X=B.defaultFormattingWidth||B.defaultWidth,Y=G!==null&&G!==void 0&&G.width?String(G.width):X;J=B.formattingValues[Y]||B.formattingValues[X]}else{var Z=B.defaultWidth,q=G!==null&&G!==void 0&&G.width?String(G.width):B.defaultWidth;J=B.values[q]||B.values[Z]}var T=B.argumentCallback?B.argumentCallback(C):C;return J[T]}}var f={narrow:["v.Chr.","n.Chr."],abbreviated:["v.Chr.","n.Chr."],wide:["vor Christus","nach Christus"]},F={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. Quartal","2. Quartal","3. Quartal","4. Quartal"]},x={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","M\xE4r","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],wide:["Januar","Februar","M\xE4rz","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember"]},v={narrow:x.narrow,abbreviated:["Jan.","Feb.","M\xE4rz","Apr.","Mai","Juni","Juli","Aug.","Sep.","Okt.","Nov.","Dez."],wide:x.wide},P={narrow:["S","M","D","M","D","F","S"],short:["So","Mo","Di","Mi","Do","Fr","Sa"],abbreviated:["So.","Mo.","Di.","Mi.","Do.","Fr.","Sa."],wide:["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"]},k={narrow:{am:"vm.",pm:"nm.",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachm.",evening:"Abend",night:"Nacht"},abbreviated:{am:"vorm.",pm:"nachm.",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachmittag",evening:"Abend",night:"Nacht"},wide:{am:"vormittags",pm:"nachmittags",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachmittag",evening:"Abend",night:"Nacht"}},b={narrow:{am:"vm.",pm:"nm.",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachm.",evening:"abends",night:"nachts"},abbreviated:{am:"vorm.",pm:"nachm.",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachmittags",evening:"abends",night:"nachts"},wide:{am:"vormittags",pm:"nachmittags",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachmittags",evening:"abends",night:"nachts"}},h=function B(C){var G=Number(C);return G+"."},m={ordinalNumber:h,era:I({values:f,defaultWidth:"wide"}),quarter:I({values:F,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:I({values:x,formattingValues:v,defaultWidth:"wide"}),day:I({values:P,defaultWidth:"wide"}),dayPeriod:I({values:k,defaultWidth:"wide",formattingValues:b,defaultFormattingWidth:"wide"})};function O(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=G.width,J=H&&B.matchPatterns[H]||B.matchPatterns[B.defaultMatchWidth],X=C.match(J);if(!X)return null;var Y=X[0],Z=H&&B.parsePatterns[H]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(Z)?c(Z,function(A){return A.test(Y)}):y(Z,function(A){return A.test(Y)}),T;T=B.valueCallback?B.valueCallback(q):q,T=G.valueCallback?G.valueCallback(T):T;var GB=C.slice(Y.length);return{value:T,rest:GB}}}function y(B,C){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&C(B[G]))return G;return}function c(B,C){for(var G=0;G<B.length;G++)if(C(B[G]))return G;return}function d(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.match(B.matchPattern);if(!H)return null;var J=H[0],X=C.match(B.parsePattern);if(!X)return null;var Y=B.valueCallback?B.valueCallback(X[0]):X[0];Y=G.valueCallback?G.valueCallback(Y):Y;var Z=C.slice(J.length);return{value:Y,rest:Z}}}var g=/^(\d+)(\.)?/i,p=/\d+/i,u={narrow:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,abbreviated:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,wide:/^(vor Christus|vor unserer Zeitrechnung|nach Christus|unserer Zeitrechnung)/i},l={any:[/^v/i,/^n/i]},i={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](\.)? Quartal/i},n={any:[/1/i,/2/i,/3/i,/4/i]},s={narrow:/^[jfmasond]/i,abbreviated:/^(j[aä]n|feb|mär[z]?|apr|mai|jun[i]?|jul[i]?|aug|sep|okt|nov|dez)\.?/i,wide:/^(januar|februar|märz|april|mai|juni|juli|august|september|oktober|november|dezember)/i},o={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^j[aä]/i,/^f/i,/^mär/i,/^ap/i,/^mai/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},r={narrow:/^[smdmf]/i,short:/^(so|mo|di|mi|do|fr|sa)/i,abbreviated:/^(son?|mon?|die?|mit?|don?|fre?|sam?)\.?/i,wide:/^(sonntag|montag|dienstag|mittwoch|donnerstag|freitag|samstag)/i},a={any:[/^so/i,/^mo/i,/^di/i,/^mi/i,/^do/i,/^f/i,/^sa/i]},e={narrow:/^(vm\.?|nm\.?|Mitternacht|Mittag|morgens|nachm\.?|abends|nachts)/i,abbreviated:/^(vorm\.?|nachm\.?|Mitternacht|Mittag|morgens|nachm\.?|abends|nachts)/i,wide:/^(vormittags|nachmittags|Mitternacht|Mittag|morgens|nachmittags|abends|nachts)/i},t={any:{am:/^v/i,pm:/^n/i,midnight:/^Mitte/i,noon:/^Mitta/i,morning:/morgens/i,afternoon:/nachmittags/i,evening:/abends/i,night:/nachts/i}},BB={ordinalNumber:d({matchPattern:g,parsePattern:p,valueCallback:function B(C){return parseInt(C)}}),era:O({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),quarter:O({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:O({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"}),day:O({matchPatterns:r,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:e,defaultMatchWidth:"wide",parsePatterns:t,defaultParseWidth:"any"})},CB={code:"de",formatDistance:M,formatLong:j,formatRelative:_,localize:m,match:BB,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{de:CB})})})();

//# debugId=EF84D3AFAE2CFF3764756E2164756E21
